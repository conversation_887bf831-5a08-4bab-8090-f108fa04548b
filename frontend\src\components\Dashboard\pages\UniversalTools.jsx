import React, { useState } from 'react';
import './UniversalTools.css';

const UniversalTools = ({ isSmartAPIConnected }) => {
  const [activeTab, setActiveTab] = useState('capital-management');
  
  // Capital Management State
  const [capitalSettings, setCapitalSettings] = useState({
    totalCapital: 1000000,
    riskPerTrade: 2,
    maxPositions: 10,
    emergencyStopLoss: 10,
    dailyLossLimit: 5
  });

  // Stock Name Trading State
  const [stockSearch, setStockSearch] = useState('');
  const [selectedStock, setSelectedStock] = useState(null);
  const [orderDetails, setOrderDetails] = useState({
    quantity: '',
    orderType: 'MARKET',
    price: ''
  });

  // BOH Filter State
  const [bohFilters, setBohFilters] = useState({
    minVolume: 100000,
    minPrice: 50,
    maxPrice: 5000,
    marketCap: 'large', // large, mid, small, all
    sector: 'all',
    excludePennyStocks: true,
    minLiquidity: 1000000
  });

  const [filteredStocks] = useState([
    { symbol: 'RELIANCE', name: 'Reliance Industries', price: 2445.30, volume: 1250000, marketCap: 'large', sector: 'Energy' },
    { symbol: 'TCS', name: 'Tata Consultancy Services', price: 3250.80, volume: 890000, marketCap: 'large', sector: 'IT' },
    { symbol: 'INFY', name: 'Infosys Limited', price: 1445.80, volume: 2100000, marketCap: 'large', sector: 'IT' },
    { symbol: 'HDFC', name: 'HDFC Bank', price: 1678.90, volume: 1800000, marketCap: 'large', sector: 'Banking' },
    { symbol: 'ICICIBANK', name: 'ICICI Bank', price: 945.60, volume: 2200000, marketCap: 'large', sector: 'Banking' }
  ]);

  const calculatePositionSize = (price, riskPercent) => {
    const riskAmount = (capitalSettings.totalCapital * riskPercent) / 100;
    const stopLossPercent = 5; // Default 5% stop loss
    const stopLossAmount = (price * stopLossPercent) / 100;
    return Math.floor(riskAmount / stopLossAmount);
  };

  const handlePlaceOrder = async () => {
    if (!selectedStock || !orderDetails.quantity) return;

    try {
      const orderData = {
        symbol: selectedStock.symbol,
        quantity: parseInt(orderDetails.quantity),
        order_type: 'BUY',
        price_type: orderDetails.orderType,
        price: orderDetails.orderType === 'LIMIT' ? parseFloat(orderDetails.price) : null
      };

      const response = await fetch('/api/trading/place-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        alert('Order placed successfully!');
        setOrderDetails({ quantity: '', orderType: 'MARKET', price: '' });
      }
    } catch (error) {
      console.error('Error placing order:', error);
    }
  };

  return (
    <div className="universal-tools">
      <div className="page-header">
        <h1>Universal Tools</h1>
        <p>Essential trading tools for better decision making</p>
      </div>

      {/* Tab Navigation */}
      <div className="tools-tabs">
        <button 
          className={`tab-btn ${activeTab === 'capital-management' ? 'active' : ''}`}
          onClick={() => setActiveTab('capital-management')}
        >
          💰 Capital Management
        </button>
        <button 
          className={`tab-btn ${activeTab === 'stock-trading' ? 'active' : ''}`}
          onClick={() => setActiveTab('stock-trading')}
        >
          📈 Stock Name Trading
        </button>
        <button 
          className={`tab-btn ${activeTab === 'boh-filter' ? 'active' : ''}`}
          onClick={() => setActiveTab('boh-filter')}
        >
          🔍 BOH Filter
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'capital-management' && (
          <div className="capital-management-tab">
            <div className="tool-header">
              <h2>Capital Management</h2>
              <p>Manage your trading capital and risk parameters</p>
            </div>

            <div className="capital-grid">
              <div className="capital-settings">
                <h3>Risk Settings</h3>
                <div className="setting-group">
                  <label>Total Capital</label>
                  <input 
                    type="number"
                    value={capitalSettings.totalCapital}
                    onChange={(e) => setCapitalSettings(prev => ({
                      ...prev,
                      totalCapital: parseInt(e.target.value)
                    }))}
                    className="setting-input"
                  />
                </div>

                <div className="setting-group">
                  <label>Risk Per Trade (%)</label>
                  <input 
                    type="number"
                    value={capitalSettings.riskPerTrade}
                    onChange={(e) => setCapitalSettings(prev => ({
                      ...prev,
                      riskPerTrade: parseFloat(e.target.value)
                    }))}
                    min="0.1"
                    max="10"
                    step="0.1"
                    className="setting-input"
                  />
                </div>

                <div className="setting-group">
                  <label>Max Positions</label>
                  <input 
                    type="number"
                    value={capitalSettings.maxPositions}
                    onChange={(e) => setCapitalSettings(prev => ({
                      ...prev,
                      maxPositions: parseInt(e.target.value)
                    }))}
                    min="1"
                    max="50"
                    className="setting-input"
                  />
                </div>

                <div className="setting-group">
                  <label>Daily Loss Limit (%)</label>
                  <input 
                    type="number"
                    value={capitalSettings.dailyLossLimit}
                    onChange={(e) => setCapitalSettings(prev => ({
                      ...prev,
                      dailyLossLimit: parseFloat(e.target.value)
                    }))}
                    min="1"
                    max="20"
                    step="0.5"
                    className="setting-input"
                  />
                </div>
              </div>

              <div className="capital-calculator">
                <h3>Position Size Calculator</h3>
                <div className="calculator-metrics">
                  <div className="metric-card">
                    <span className="metric-label">Risk Amount</span>
                    <span className="metric-value">
                      ₹{((capitalSettings.totalCapital * capitalSettings.riskPerTrade) / 100).toLocaleString()}
                    </span>
                  </div>
                  <div className="metric-card">
                    <span className="metric-label">Max Daily Loss</span>
                    <span className="metric-value">
                      ₹{((capitalSettings.totalCapital * capitalSettings.dailyLossLimit) / 100).toLocaleString()}
                    </span>
                  </div>
                  <div className="metric-card">
                    <span className="metric-label">Capital Per Position</span>
                    <span className="metric-value">
                      ₹{(capitalSettings.totalCapital / capitalSettings.maxPositions).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'stock-trading' && (
          <div className="stock-trading-tab">
            <div className="tool-header">
              <h2>Stock Name Trading</h2>
              <p>Search and trade stocks by name</p>
            </div>

            <div className="trading-interface">
              <div className="stock-search">
                <h3>Search Stock</h3>
                <input 
                  type="text"
                  placeholder="Enter stock name or symbol..."
                  value={stockSearch}
                  onChange={(e) => setStockSearch(e.target.value)}
                  className="search-input"
                />
                
                {stockSearch && (
                  <div className="search-results">
                    {filteredStocks
                      .filter(stock => 
                        stock.symbol.toLowerCase().includes(stockSearch.toLowerCase()) ||
                        stock.name.toLowerCase().includes(stockSearch.toLowerCase())
                      )
                      .map(stock => (
                        <div 
                          key={stock.symbol}
                          className="search-result"
                          onClick={() => {
                            setSelectedStock(stock);
                            setStockSearch(stock.symbol);
                          }}
                        >
                          <div className="stock-info">
                            <span className="stock-symbol">{stock.symbol}</span>
                            <span className="stock-name">{stock.name}</span>
                          </div>
                          <span className="stock-price">₹{stock.price}</span>
                        </div>
                      ))
                    }
                  </div>
                )}
              </div>

              {selectedStock && (
                <div className="order-form">
                  <h3>Place Order - {selectedStock.symbol}</h3>
                  <div className="stock-details">
                    <div className="detail-item">
                      <span>Current Price:</span>
                      <span>₹{selectedStock.price}</span>
                    </div>
                    <div className="detail-item">
                      <span>Volume:</span>
                      <span>{(selectedStock.volume / 1000000).toFixed(1)}M</span>
                    </div>
                  </div>

                  <div className="order-inputs">
                    <div className="input-group">
                      <label>Order Type</label>
                      <select 
                        value={orderDetails.orderType}
                        onChange={(e) => setOrderDetails(prev => ({
                          ...prev,
                          orderType: e.target.value
                        }))}
                        className="order-select"
                      >
                        <option value="MARKET">Market</option>
                        <option value="LIMIT">Limit</option>
                      </select>
                    </div>

                    {orderDetails.orderType === 'LIMIT' && (
                      <div className="input-group">
                        <label>Limit Price</label>
                        <input 
                          type="number"
                          value={orderDetails.price}
                          onChange={(e) => setOrderDetails(prev => ({
                            ...prev,
                            price: e.target.value
                          }))}
                          placeholder="Enter limit price"
                          className="order-input"
                        />
                      </div>
                    )}

                    <div className="input-group">
                      <label>Quantity</label>
                      <input 
                        type="number"
                        value={orderDetails.quantity}
                        onChange={(e) => setOrderDetails(prev => ({
                          ...prev,
                          quantity: e.target.value
                        }))}
                        placeholder="Enter quantity"
                        className="order-input"
                      />
                    </div>

                    <div className="suggested-quantity">
                      <span>Suggested Quantity (2% risk): </span>
                      <span className="suggestion">
                        {calculatePositionSize(selectedStock.price, capitalSettings.riskPerTrade)} shares
                      </span>
                    </div>
                  </div>

                  <button 
                    className="place-order-btn"
                    onClick={handlePlaceOrder}
                    disabled={!isSmartAPIConnected || !orderDetails.quantity}
                  >
                    {isSmartAPIConnected ? 'Place Buy Order' : 'Connect Angel One First'}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'boh-filter' && (
          <div className="boh-filter-tab">
            <div className="tool-header">
              <h2>BOH Filter</h2>
              <p>Filter stocks based on your criteria</p>
            </div>

            <div className="filter-interface">
              <div className="filter-controls">
                <h3>Filter Criteria</h3>
                
                <div className="filter-group">
                  <label>Minimum Volume</label>
                  <input 
                    type="number"
                    value={bohFilters.minVolume}
                    onChange={(e) => setBohFilters(prev => ({
                      ...prev,
                      minVolume: parseInt(e.target.value)
                    }))}
                    className="filter-input"
                  />
                </div>

                <div className="filter-group">
                  <label>Price Range</label>
                  <div className="price-range">
                    <input 
                      type="number"
                      value={bohFilters.minPrice}
                      onChange={(e) => setBohFilters(prev => ({
                        ...prev,
                        minPrice: parseInt(e.target.value)
                      }))}
                      placeholder="Min"
                      className="filter-input small"
                    />
                    <span>to</span>
                    <input 
                      type="number"
                      value={bohFilters.maxPrice}
                      onChange={(e) => setBohFilters(prev => ({
                        ...prev,
                        maxPrice: parseInt(e.target.value)
                      }))}
                      placeholder="Max"
                      className="filter-input small"
                    />
                  </div>
                </div>

                <div className="filter-group">
                  <label>Market Cap</label>
                  <select 
                    value={bohFilters.marketCap}
                    onChange={(e) => setBohFilters(prev => ({
                      ...prev,
                      marketCap: e.target.value
                    }))}
                    className="filter-select"
                  >
                    <option value="all">All</option>
                    <option value="large">Large Cap</option>
                    <option value="mid">Mid Cap</option>
                    <option value="small">Small Cap</option>
                  </select>
                </div>

                <div className="filter-group">
                  <label>Sector</label>
                  <select 
                    value={bohFilters.sector}
                    onChange={(e) => setBohFilters(prev => ({
                      ...prev,
                      sector: e.target.value
                    }))}
                    className="filter-select"
                  >
                    <option value="all">All Sectors</option>
                    <option value="Banking">Banking</option>
                    <option value="IT">Information Technology</option>
                    <option value="Energy">Energy</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="FMCG">FMCG</option>
                  </select>
                </div>
              </div>

              <div className="filtered-results">
                <h3>Filtered Stocks ({filteredStocks.length})</h3>
                <div className="stocks-list">
                  {filteredStocks.map(stock => (
                    <div key={stock.symbol} className="stock-item">
                      <div className="stock-main">
                        <div className="stock-identity">
                          <span className="symbol">{stock.symbol}</span>
                          <span className="name">{stock.name}</span>
                        </div>
                        <div className="stock-metrics">
                          <span className="price">₹{stock.price}</span>
                          <span className="volume">{(stock.volume / 1000000).toFixed(1)}M vol</span>
                        </div>
                      </div>
                      <div className="stock-tags">
                        <span className="tag market-cap">{stock.marketCap} cap</span>
                        <span className="tag sector">{stock.sector}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UniversalTools;
