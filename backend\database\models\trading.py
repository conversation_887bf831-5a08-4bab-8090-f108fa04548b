from sqlalchemy import Column, Integer, String, DateTime, Numeric, ForeignKey, Enum, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.database import Base
import enum

class OrderType(enum.Enum):
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(enum.Enum):
    PENDING = "PENDING"
    EXECUTED = "EXECUTED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    PARTIAL = "PARTIAL"

class PriceType(enum.Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    STOP_LOSS_MARKET = "STOP_LOSS_MARKET"

class Order(Base):
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    portfolio_id = Column(Integer, ForeignKey("portfolios.id"), nullable=False)
    
    # Order identification
    order_id = Column(String(50), unique=True, nullable=False)  # SmartAPI order ID
    client_order_id = Column(String(50), nullable=True)  # Our internal order ID
    
    # Order details
    symbol = Column(String(20), nullable=False)
    company_name = Column(String(200), nullable=True)
    order_type = Column(Enum(OrderType), nullable=False)
    price_type = Column(Enum(PriceType), nullable=False)
    
    # Quantities and prices
    quantity = Column(Integer, nullable=False)
    executed_quantity = Column(Integer, default=0)
    price = Column(Numeric(10, 2), nullable=True)  # Limit price
    executed_price = Column(Numeric(10, 2), nullable=True)  # Average executed price
    
    # Stop loss details
    stop_loss_price = Column(Numeric(10, 2), nullable=True)
    trigger_price = Column(Numeric(10, 2), nullable=True)
    
    # Order status and metadata
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING)
    exchange = Column(String(10), default="NSE")
    product_type = Column(String(20), default="INTRADAY")  # INTRADAY, DELIVERY, etc.
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    executed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Error handling
    rejection_reason = Column(Text, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="orders")
    portfolio = relationship("Portfolio", back_populates="orders")
    
    def __repr__(self):
        return f"<Order(id={self.id}, symbol='{self.symbol}', type='{self.order_type}', status='{self.status}')>"

class Trade(Base):
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False)
    
    # Trade identification
    trade_id = Column(String(50), unique=True, nullable=False)  # SmartAPI trade ID
    
    # Trade details
    symbol = Column(String(20), nullable=False)
    trade_type = Column(Enum(OrderType), nullable=False)
    quantity = Column(Integer, nullable=False)
    price = Column(Numeric(10, 2), nullable=False)
    
    # Financial details
    trade_value = Column(Numeric(15, 2), nullable=False)
    brokerage = Column(Numeric(10, 2), default=0.00)
    taxes = Column(Numeric(10, 2), default=0.00)
    net_amount = Column(Numeric(15, 2), nullable=False)
    
    # Timestamps
    trade_time = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="trades")
    order = relationship("Order", back_populates="trades")
    
    def __repr__(self):
        return f"<Trade(id={self.id}, symbol='{self.symbol}', quantity={self.quantity}, price={self.price})>"
