import React, { useState } from 'react';
import './Backtesting.css';

const Backtesting = ({ userSubscription }) => {
  const [backtestConfig, setBacktestConfig] = useState({
    strategy: 'weekly-high',
    startDate: '2023-01-01',
    endDate: '2024-01-15',
    initialCapital: 1000000,
    symbols: ['RELIANCE', 'TCS', 'INFY', 'HDFC', 'ICICIBANK'],
    riskPerTrade: 2,
    maxPositions: 10
  });

  const [backtestResults, setBacktestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);

  const [sampleResults] = useState({
    totalReturn: 24.5,
    annualizedReturn: 18.2,
    maxDrawdown: -8.3,
    sharpeRatio: 1.45,
    winRate: 68.5,
    totalTrades: 147,
    winningTrades: 101,
    losingTrades: 46,
    avgWin: 4.2,
    avgLoss: -2.1,
    profitFactor: 2.1,
    monthlyReturns: [
      { month: 'Jan 2023', return: 3.2 },
      { month: 'Feb 2023', return: -1.5 },
      { month: 'Mar 2023', return: 5.8 },
      { month: 'Apr 2023', return: 2.1 },
      { month: 'May 2023', return: -0.8 },
      { month: 'Jun 2023', return: 4.5 },
      { month: 'Jul 2023', return: 1.9 },
      { month: 'Aug 2023', return: -2.3 },
      { month: 'Sep 2023', return: 6.2 },
      { month: 'Oct 2023', return: 3.7 },
      { month: 'Nov 2023', return: 1.4 },
      { month: 'Dec 2023', return: 2.8 }
    ],
    trades: [
      { date: '2023-12-15', symbol: 'RELIANCE', action: 'BUY', price: 2400, quantity: 10, pnl: 450, return: 1.9 },
      { date: '2023-12-10', symbol: 'TCS', action: 'SELL', price: 3200, quantity: 5, pnl: -120, return: -0.8 },
      { date: '2023-12-08', symbol: 'INFY', action: 'BUY', price: 1420, quantity: 15, pnl: 380, return: 1.8 },
      { date: '2023-12-05', symbol: 'HDFC', action: 'BUY', price: 1650, quantity: 8, pnl: 240, return: 1.8 },
      { date: '2023-12-01', symbol: 'ICICIBANK', action: 'SELL', price: 920, quantity: 20, pnl: 160, return: 0.9 }
    ]
  });

  const handleRunBacktest = async () => {
    setIsRunning(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // In real implementation, call the backtesting API
      const response = await fetch('/api/backtesting/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(backtestConfig)
      });

      if (response.ok) {
        const results = await response.json();
        setBacktestResults(results);
      } else {
        // Use sample results for demo
        setBacktestResults(sampleResults);
      }
    } catch (error) {
      console.error('Backtest error:', error);
      // Use sample results for demo
      setBacktestResults(sampleResults);
    } finally {
      setIsRunning(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="backtesting-page">
      <div className="page-header">
        <h1>Backtesting</h1>
        <p>Test your strategies against historical data</p>
      </div>

      <div className="backtesting-container">
        {/* Configuration Panel */}
        <div className="backtest-config">
          <h2>Backtest Configuration</h2>
          
          <div className="config-form">
            <div className="form-group">
              <label>Strategy</label>
              <select 
                value={backtestConfig.strategy}
                onChange={(e) => setBacktestConfig(prev => ({
                  ...prev,
                  strategy: e.target.value
                }))}
                className="config-select"
              >
                <option value="weekly-high">Weekly High Strategy</option>
                <option value="rsi" disabled={userSubscription === 'starter'}>
                  RSI Strategy {userSubscription === 'starter' ? '(Pro)' : ''}
                </option>
                <option value="breakout" disabled={userSubscription !== 'enterprise'}>
                  Consolidated Breakout {userSubscription !== 'enterprise' ? '(Enterprise)' : ''}
                </option>
              </select>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>Start Date</label>
                <input 
                  type="date"
                  value={backtestConfig.startDate}
                  onChange={(e) => setBacktestConfig(prev => ({
                    ...prev,
                    startDate: e.target.value
                  }))}
                  className="config-input"
                />
              </div>
              <div className="form-group">
                <label>End Date</label>
                <input 
                  type="date"
                  value={backtestConfig.endDate}
                  onChange={(e) => setBacktestConfig(prev => ({
                    ...prev,
                    endDate: e.target.value
                  }))}
                  className="config-input"
                />
              </div>
            </div>

            <div className="form-group">
              <label>Initial Capital</label>
              <input 
                type="number"
                value={backtestConfig.initialCapital}
                onChange={(e) => setBacktestConfig(prev => ({
                  ...prev,
                  initialCapital: parseInt(e.target.value)
                }))}
                className="config-input"
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label>Risk Per Trade (%)</label>
                <input 
                  type="number"
                  value={backtestConfig.riskPerTrade}
                  onChange={(e) => setBacktestConfig(prev => ({
                    ...prev,
                    riskPerTrade: parseFloat(e.target.value)
                  }))}
                  min="0.1"
                  max="10"
                  step="0.1"
                  className="config-input"
                />
              </div>
              <div className="form-group">
                <label>Max Positions</label>
                <input 
                  type="number"
                  value={backtestConfig.maxPositions}
                  onChange={(e) => setBacktestConfig(prev => ({
                    ...prev,
                    maxPositions: parseInt(e.target.value)
                  }))}
                  min="1"
                  max="50"
                  className="config-input"
                />
              </div>
            </div>

            <div className="form-group">
              <label>Symbols (comma separated)</label>
              <input 
                type="text"
                value={backtestConfig.symbols.join(', ')}
                onChange={(e) => setBacktestConfig(prev => ({
                  ...prev,
                  symbols: e.target.value.split(',').map(s => s.trim())
                }))}
                placeholder="RELIANCE, TCS, INFY, HDFC"
                className="config-input"
              />
            </div>

            <button 
              className="run-backtest-btn"
              onClick={handleRunBacktest}
              disabled={isRunning}
            >
              {isRunning ? 'Running Backtest...' : 'Run Backtest'}
            </button>
          </div>
        </div>

        {/* Results Panel */}
        <div className="backtest-results">
          {isRunning && (
            <div className="running-indicator">
              <div className="loading-spinner"></div>
              <h3>Running Backtest...</h3>
              <p>Analyzing historical data and generating results</p>
            </div>
          )}

          {backtestResults && !isRunning && (
            <div className="results-content">
              <h2>Backtest Results</h2>
              
              {/* Key Metrics */}
              <div className="results-metrics">
                <div className="metric-card">
                  <span className="metric-label">Total Return</span>
                  <span className="metric-value positive">+{backtestResults.totalReturn}%</span>
                </div>
                <div className="metric-card">
                  <span className="metric-label">Annualized Return</span>
                  <span className="metric-value positive">+{backtestResults.annualizedReturn}%</span>
                </div>
                <div className="metric-card">
                  <span className="metric-label">Max Drawdown</span>
                  <span className="metric-value negative">{backtestResults.maxDrawdown}%</span>
                </div>
                <div className="metric-card">
                  <span className="metric-label">Sharpe Ratio</span>
                  <span className="metric-value">{backtestResults.sharpeRatio}</span>
                </div>
                <div className="metric-card">
                  <span className="metric-label">Win Rate</span>
                  <span className="metric-value">{backtestResults.winRate}%</span>
                </div>
                <div className="metric-card">
                  <span className="metric-label">Total Trades</span>
                  <span className="metric-value">{backtestResults.totalTrades}</span>
                </div>
              </div>

              {/* Performance Chart */}
              <div className="performance-chart">
                <h3>Monthly Returns</h3>
                <div className="chart-container">
                  {backtestResults.monthlyReturns.map((month, index) => (
                    <div key={index} className="chart-bar">
                      <div 
                        className={`bar ${month.return >= 0 ? 'positive' : 'negative'}`}
                        style={{ 
                          height: `${Math.abs(month.return) * 10}px`,
                          minHeight: '5px'
                        }}
                      ></div>
                      <span className="bar-label">{month.month.split(' ')[0]}</span>
                      <span className="bar-value">{month.return}%</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Trade Analysis */}
              <div className="trade-analysis">
                <h3>Trade Analysis</h3>
                <div className="analysis-stats">
                  <div className="stat-item">
                    <span className="stat-label">Winning Trades</span>
                    <span className="stat-value positive">{backtestResults.winningTrades}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Losing Trades</span>
                    <span className="stat-value negative">{backtestResults.losingTrades}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Avg Win</span>
                    <span className="stat-value positive">+{backtestResults.avgWin}%</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Avg Loss</span>
                    <span className="stat-value negative">{backtestResults.avgLoss}%</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-label">Profit Factor</span>
                    <span className="stat-value">{backtestResults.profitFactor}</span>
                  </div>
                </div>
              </div>

              {/* Recent Trades */}
              <div className="recent-trades">
                <h3>Recent Trades</h3>
                <div className="trades-table">
                  <div className="table-header">
                    <span>Date</span>
                    <span>Symbol</span>
                    <span>Action</span>
                    <span>Price</span>
                    <span>Quantity</span>
                    <span>P&L</span>
                    <span>Return</span>
                  </div>
                  {backtestResults.trades.map((trade, index) => (
                    <div key={index} className="table-row">
                      <span>{trade.date}</span>
                      <span>{trade.symbol}</span>
                      <span className={`action ${trade.action.toLowerCase()}`}>{trade.action}</span>
                      <span>₹{trade.price}</span>
                      <span>{trade.quantity}</span>
                      <span className={trade.pnl >= 0 ? 'positive' : 'negative'}>
                        ₹{trade.pnl}
                      </span>
                      <span className={trade.return >= 0 ? 'positive' : 'negative'}>
                        {trade.return >= 0 ? '+' : ''}{trade.return}%
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {!backtestResults && !isRunning && (
            <div className="no-results">
              <div className="no-results-icon">📊</div>
              <h3>No Results Yet</h3>
              <p>Configure your backtest parameters and click "Run Backtest" to see results</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Backtesting;
