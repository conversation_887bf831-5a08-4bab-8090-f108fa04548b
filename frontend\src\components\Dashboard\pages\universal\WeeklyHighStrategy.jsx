import React, { useState, useEffect } from 'react';
import { Card, Button, Badge } from '../../../UI';
import { fetchNifty200StockData, fetchWeeklyOHLCData, getLastWeekHighestPrice } from '../../../../services/yahooFinanceService';
import './WeeklyHighStrategy.css';

const WeeklyHighStrategy = () => {
  const [stocks, setStocks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedStocks, setExpandedStocks] = useState(new Set());
  const [lastSignalTime, setLastSignalTime] = useState(null);
  const [nextSignalTime, setNextSignalTime] = useState(null);

  // Mock current holdings for filtering (replace with actual data)
  const [currentHoldings] = useState(['RELIANCE', 'TCS', 'INFY']);

  useEffect(() => {
    fetchWeeklyHighData();
    calculateNextSignalTime();
  }, []);

  const fetchWeeklyHighData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch BOH eligible stocks
      const allStocks = await fetchNifty200StockData();
      const bohEligibleStocks = allStocks.filter(stock => stock.bohEligible);

      // Filter out stocks already in holdings
      const availableStocks = bohEligibleStocks.filter(stock => 
        !currentHoldings.includes(stock.symbol)
      );

      // Fetch weekly data for each stock
      const weeklyHighStocks = await Promise.all(
        availableStocks.map(async (stock) => {
          try {
            const lastWeekHigh = await getLastWeekHighestPrice(stock.symbol);
            const suggestedBuyPrice = lastWeekHigh + 0.05;
            const percentDifference = ((stock.currentPrice - suggestedBuyPrice) / suggestedBuyPrice) * 100;
            const suggestedGTTQuantity = Math.floor(2000 / suggestedBuyPrice);

            return {
              ...stock,
              lastWeekHighestPrice: lastWeekHigh,
              suggestedBuyPrice,
              percentDifference,
              suggestedGTTQuantity,
              weeklyOHLCData: null // Will be loaded when expanded
            };
          } catch (error) {
            console.warn(`Failed to fetch weekly data for ${stock.symbol}:`, error);
            return null;
          }
        })
      );

      setStocks(weeklyHighStocks.filter(stock => stock !== null));
    } catch (err) {
      setError('Failed to fetch Weekly High strategy data. Please try again.');
      console.error('Weekly High fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateNextSignalTime = () => {
    const now = new Date();
    const nextFriday = new Date(now);
    
    // Find next Friday
    const daysUntilFriday = (5 - now.getDay() + 7) % 7;
    if (daysUntilFriday === 0 && now.getHours() >= 20) {
      // If it's Friday after 8 PM, get next Friday
      nextFriday.setDate(now.getDate() + 7);
    } else {
      nextFriday.setDate(now.getDate() + daysUntilFriday);
    }
    
    nextFriday.setHours(20, 0, 0, 0); // 8:00 PM
    setNextSignalTime(nextFriday);

    // Set last signal time (previous Friday 8 PM)
    const lastFriday = new Date(nextFriday);
    lastFriday.setDate(nextFriday.getDate() - 7);
    setLastSignalTime(lastFriday);
  };

  const toggleStockExpansion = async (stockSymbol) => {
    const newExpanded = new Set(expandedStocks);
    
    if (newExpanded.has(stockSymbol)) {
      newExpanded.delete(stockSymbol);
    } else {
      newExpanded.add(stockSymbol);
      
      // Load weekly OHLC data if not already loaded
      const stockIndex = stocks.findIndex(s => s.symbol === stockSymbol);
      if (stockIndex >= 0 && !stocks[stockIndex].weeklyOHLCData) {
        try {
          const weeklyData = await fetchWeeklyOHLCData(stockSymbol, 2);
          const updatedStocks = [...stocks];
          updatedStocks[stockIndex].weeklyOHLCData = weeklyData;
          setStocks(updatedStocks);
        } catch (error) {
          console.error(`Failed to load OHLC data for ${stockSymbol}:`, error);
        }
      }
    }
    
    setExpandedStocks(newExpanded);
  };

  const formatPrice = (price) => {
    return `₹${price?.toFixed(2) || '0.00'}`;
  };

  const formatPercentage = (percent) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent?.toFixed(2) || '0.00'}%`;
  };

  const formatDateTime = (date) => {
    if (!date) return 'N/A';
    return date.toLocaleString('en-IN', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderOHLCTable = (stock) => {
    if (!stock.weeklyOHLCData || stock.weeklyOHLCData.length === 0) {
      return (
        <div className="ohlc-loading">
          <p>Loading weekly OHLC data...</p>
        </div>
      );
    }

    const lastWeek = stock.weeklyOHLCData[stock.weeklyOHLCData.length - 1];
    
    return (
      <div className="ohlc-table-container">
        <h4>Last Week's OHLC Data ({lastWeek.weekStart} to {lastWeek.weekEnd})</h4>
        <table className="ohlc-table">
          <thead>
            <tr>
              <th>Day</th>
              <th>Date</th>
              <th>Open</th>
              <th>High</th>
              <th>Low</th>
              <th>Close</th>
              <th>Last Week's Highest Price</th>
            </tr>
          </thead>
          <tbody>
            {lastWeek.days.map((day, index) => (
              <tr key={day.date}>
                <td>{day.dayName}</td>
                <td>{day.date}</td>
                <td>{formatPrice(day.open)}</td>
                <td className="high-price">{formatPrice(day.high)}</td>
                <td className="low-price">{formatPrice(day.low)}</td>
                <td>{formatPrice(day.close)}</td>
                <td className="weekly-high">
                  {index === lastWeek.days.length - 1 ? formatPrice(lastWeek.weeklyHigh) : ''}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="weekly-high-loading">
        <Card variant="elevated">
          <Card.Content>
            <div className="loading-content">
              <div className="loading-spinner"></div>
              <p>Analyzing BOH eligible stocks and calculating weekly high breakouts...</p>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="weekly-high-error">
        <Card variant="elevated">
          <Card.Content>
            <div className="error-content">
              <div className="error-icon">⚠️</div>
              <h3>Error Loading Data</h3>
              <p>{error}</p>
              <Button onClick={fetchWeeklyHighData} variant="primary">
                Retry
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="weekly-high-strategy">
      <div className="strategy-header">
        <div className="strategy-info">
          <h3>Weekly High Breakout Strategy</h3>
          <p>Identifying breakout opportunities from BOH eligible stocks</p>
          <div className="signal-timing">
            <Badge variant="primary">
              Last Signal: {formatDateTime(lastSignalTime)}
            </Badge>
            <Badge variant="success">
              Next Signal: {formatDateTime(nextSignalTime)}
            </Badge>
          </div>
        </div>
      </div>

      <div className="strategy-summary">
        <Card variant="elevated">
          <Card.Content>
            <div className="summary-grid">
              <div className="summary-item">
                <span className="summary-label">BOH Eligible Stocks</span>
                <span className="summary-value">{stocks.length}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Excluded (Holdings)</span>
                <span className="summary-value">{currentHoldings.length}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Max Investment per Trade</span>
                <span className="summary-value">₹2,000</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Signal Generation</span>
                <span className="summary-value">Friday 8:00 PM</span>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="weekly-high-table">
        <Card variant="elevated">
          <Card.Header>
            <Card.Title>Weekly High Strategy Signals ({stocks.length} stocks)</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="table-container">
              <table className="strategy-table">
                <thead>
                  <tr>
                    <th>Stock</th>
                    <th>CMP</th>
                    <th>Last Week's High</th>
                    <th>Suggested Buy Price</th>
                    <th>% Difference</th>
                    <th>GTT Quantity</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {stocks.map((stock) => (
                    <React.Fragment key={stock.symbol}>
                      <tr className="stock-row">
                        <td>
                          <div className="stock-info">
                            <span className="stock-symbol">{stock.symbol}</span>
                            <span className="stock-name">{stock.name}</span>
                          </div>
                        </td>
                        <td className="price-cell">
                          {formatPrice(stock.currentPrice)}
                        </td>
                        <td className="price-cell">
                          {formatPrice(stock.lastWeekHighestPrice)}
                        </td>
                        <td className="buy-price-cell">
                          {formatPrice(stock.suggestedBuyPrice)}
                        </td>
                        <td className={`percentage-cell ${stock.percentDifference >= 0 ? 'positive' : 'negative'}`}>
                          {formatPercentage(stock.percentDifference)}
                        </td>
                        <td className="quantity-cell">
                          {stock.suggestedGTTQuantity}
                        </td>
                        <td className="actions-cell">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleStockExpansion(stock.symbol)}
                          >
                            {expandedStocks.has(stock.symbol) ? '▲' : '▼'} OHLC
                          </Button>
                        </td>
                      </tr>
                      {expandedStocks.has(stock.symbol) && (
                        <tr className="expanded-row">
                          <td colSpan="7">
                            {renderOHLCTable(stock)}
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
};

export default WeeklyHighStrategy;
