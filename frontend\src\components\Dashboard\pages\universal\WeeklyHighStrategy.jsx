import React, { useState, useEffect } from 'react';
import { Card, Button, Badge } from '../../../UI';
import { fetchNifty200StockData, fetchWeeklyOHLCData, getLastWeekHighestPrice } from '../../../../services/yahooFinanceService';
import './WeeklyHighStrategy.css';

const WeeklyHighStrategy = () => {
  const [stocks, setStocks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [expandedStocks, setExpandedStocks] = useState(new Set());
  const [lastSignalTime, setLastSignalTime] = useState(null);
  const [nextSignalTime, setNextSignalTime] = useState(null);

  // Mock current holdings for filtering (replace with actual data)
  const [currentHoldings] = useState(['RELIANCE', 'TCS', 'INFY']);

  useEffect(() => {
    fetchWeeklyHighData();
    calculateNextSignalTime();
  }, []);

  const fetchWeeklyHighData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock data for demonstration - replace with real API calls later
      const mockStocks = [
        {
          symbol: 'WIPRO',
          name: 'Wipro Limited',
          currentPrice: 445.50,
          lastWeekHighestPrice: 445.00,
          suggestedBuyPrice: 445.05,
          percentDifference: 0.10,
          suggestedGTTQuantity: 4,
          bohEligible: true
        },
        {
          symbol: 'TECHM',
          name: 'Tech Mahindra Limited',
          currentPrice: 1650.75,
          lastWeekHighestPrice: 1650.00,
          suggestedBuyPrice: 1650.05,
          percentDifference: 0.04,
          suggestedGTTQuantity: 1,
          bohEligible: true
        },
        {
          symbol: 'HCLTECH',
          name: 'HCL Technologies Limited',
          currentPrice: 1420.30,
          lastWeekHighestPrice: 1420.00,
          suggestedBuyPrice: 1420.05,
          percentDifference: 0.02,
          suggestedGTTQuantity: 1,
          bohEligible: true
        }
      ];

      // Filter out stocks already in holdings
      const availableStocks = mockStocks.filter(stock =>
        !currentHoldings.includes(stock.symbol)
      );

      setStocks(availableStocks);
    } catch (err) {
      setError('Failed to fetch Weekly High strategy data. Please try again.');
      console.error('Weekly High fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateNextSignalTime = () => {
    const now = new Date();
    const nextFriday = new Date(now);
    
    // Find next Friday
    const daysUntilFriday = (5 - now.getDay() + 7) % 7;
    if (daysUntilFriday === 0 && now.getHours() >= 20) {
      // If it's Friday after 8 PM, get next Friday
      nextFriday.setDate(now.getDate() + 7);
    } else {
      nextFriday.setDate(now.getDate() + daysUntilFriday);
    }
    
    nextFriday.setHours(20, 0, 0, 0); // 8:00 PM
    setNextSignalTime(nextFriday);

    // Set last signal time (previous Friday 8 PM)
    const lastFriday = new Date(nextFriday);
    lastFriday.setDate(nextFriday.getDate() - 7);
    setLastSignalTime(lastFriday);
  };

  const toggleStockExpansion = async (stockSymbol) => {
    const newExpanded = new Set(expandedStocks);
    
    if (newExpanded.has(stockSymbol)) {
      newExpanded.delete(stockSymbol);
    } else {
      newExpanded.add(stockSymbol);
      
      // Load mock weekly OHLC data if not already loaded
      const stockIndex = stocks.findIndex(s => s.symbol === stockSymbol);
      if (stockIndex >= 0 && !stocks[stockIndex].weeklyOHLCData) {
        // Mock OHLC data for demonstration
        const mockWeeklyData = [
          {
            weekStart: '2024-01-08',
            weekEnd: '2024-01-12',
            weeklyHigh: stocks[stockIndex].lastWeekHighestPrice,
            days: [
              { dayName: 'Monday', date: '2024-01-08', open: 440.00, high: 442.50, low: 438.75, close: 441.25 },
              { dayName: 'Tuesday', date: '2024-01-09', open: 441.25, high: 443.80, low: 440.50, close: 442.90 },
              { dayName: 'Wednesday', date: '2024-01-10', open: 442.90, high: 445.00, low: 441.20, close: 444.15 },
              { dayName: 'Thursday', date: '2024-01-11', open: 444.15, high: 445.00, low: 442.80, close: 443.60 },
              { dayName: 'Friday', date: '2024-01-12', open: 443.60, high: 444.90, low: 442.10, close: 444.25 }
            ]
          }
        ];

        const updatedStocks = [...stocks];
        updatedStocks[stockIndex].weeklyOHLCData = mockWeeklyData;
        setStocks(updatedStocks);
      }
    }
    
    setExpandedStocks(newExpanded);
  };

  const formatPrice = (price) => {
    return `₹${price?.toFixed(2) || '0.00'}`;
  };

  const formatPercentage = (percent) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent?.toFixed(2) || '0.00'}%`;
  };

  const formatDateTime = (date) => {
    if (!date) return 'N/A';
    return date.toLocaleString('en-IN', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderOHLCTable = (stock) => {
    if (!stock.weeklyOHLCData || stock.weeklyOHLCData.length === 0) {
      return (
        <div className="ohlc-loading">
          <p>Loading weekly OHLC data...</p>
        </div>
      );
    }

    const lastWeek = stock.weeklyOHLCData[stock.weeklyOHLCData.length - 1];
    
    return (
      <div className="ohlc-table-container">
        <h4>Last Week's OHLC Data ({lastWeek.weekStart} to {lastWeek.weekEnd})</h4>
        <table className="ohlc-table">
          <thead>
            <tr>
              <th>Day</th>
              <th>Date</th>
              <th>Open</th>
              <th>High</th>
              <th>Low</th>
              <th>Close</th>
              <th>Last Week's Highest Price</th>
            </tr>
          </thead>
          <tbody>
            {lastWeek.days.map((day, index) => (
              <tr key={day.date}>
                <td>{day.dayName}</td>
                <td>{day.date}</td>
                <td>{formatPrice(day.open)}</td>
                <td className="high-price">{formatPrice(day.high)}</td>
                <td className="low-price">{formatPrice(day.low)}</td>
                <td>{formatPrice(day.close)}</td>
                <td className="weekly-high">
                  {index === lastWeek.days.length - 1 ? formatPrice(lastWeek.weeklyHigh) : ''}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="weekly-high-loading">
        <Card variant="elevated">
          <Card.Content>
            <div className="loading-content">
              <div className="loading-spinner"></div>
              <p>Analyzing BOH eligible stocks and calculating weekly high breakouts...</p>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="weekly-high-error">
        <Card variant="elevated">
          <Card.Content>
            <div className="error-content">
              <div className="error-icon">⚠️</div>
              <h3>Error Loading Data</h3>
              <p>{error}</p>
              <Button onClick={fetchWeeklyHighData} variant="primary">
                Retry
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="weekly-high-strategy">
      <div className="strategy-header">
        <div className="strategy-info">
          <h3>Weekly High Breakout Strategy</h3>
          <p>Identifying breakout opportunities from BOH eligible stocks</p>
          <div className="signal-timing">
            <Badge variant="primary">
              Last Signal: {formatDateTime(lastSignalTime)}
            </Badge>
            <Badge variant="success">
              Next Signal: {formatDateTime(nextSignalTime)}
            </Badge>
          </div>
        </div>
      </div>

      <div className="strategy-summary">
        <Card variant="elevated">
          <Card.Content>
            <div className="summary-grid">
              <div className="summary-item">
                <span className="summary-label">BOH Eligible Stocks</span>
                <span className="summary-value">{stocks.length}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Excluded (Holdings)</span>
                <span className="summary-value">{currentHoldings.length}</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Max Investment per Trade</span>
                <span className="summary-value">₹2,000</span>
              </div>
              <div className="summary-item">
                <span className="summary-label">Signal Generation</span>
                <span className="summary-value">Friday 8:00 PM</span>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="weekly-high-table">
        <Card variant="elevated">
          <Card.Header>
            <Card.Title>Weekly High Strategy Signals ({stocks.length} stocks)</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="table-container">
              <table className="strategy-table">
                <thead>
                  <tr>
                    <th>Stock</th>
                    <th>CMP</th>
                    <th>Last Week's High</th>
                    <th>Suggested Buy Price</th>
                    <th>% Difference</th>
                    <th>GTT Quantity</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {stocks.map((stock) => (
                    <React.Fragment key={stock.symbol}>
                      <tr className="stock-row">
                        <td>
                          <div className="stock-info">
                            <span className="stock-symbol">{stock.symbol}</span>
                            <span className="stock-name">{stock.name}</span>
                          </div>
                        </td>
                        <td className="price-cell">
                          {formatPrice(stock.currentPrice)}
                        </td>
                        <td className="price-cell">
                          {formatPrice(stock.lastWeekHighestPrice)}
                        </td>
                        <td className="buy-price-cell">
                          {formatPrice(stock.suggestedBuyPrice)}
                        </td>
                        <td className={`percentage-cell ${stock.percentDifference >= 0 ? 'positive' : 'negative'}`}>
                          {formatPercentage(stock.percentDifference)}
                        </td>
                        <td className="quantity-cell">
                          {stock.suggestedGTTQuantity}
                        </td>
                        <td className="actions-cell">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => toggleStockExpansion(stock.symbol)}
                          >
                            {expandedStocks.has(stock.symbol) ? '▲' : '▼'} OHLC
                          </Button>
                        </td>
                      </tr>
                      {expandedStocks.has(stock.symbol) && (
                        <tr className="expanded-row">
                          <td colSpan="7">
                            {renderOHLCTable(stock)}
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
};

export default WeeklyHighStrategy;
