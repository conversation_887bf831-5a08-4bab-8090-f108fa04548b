from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, DateTime, <PERSON><PERSON><PERSON>, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.database import Base

class SmartAPICredentials(Base):
    __tablename__ = "smartapi_credentials"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    
    # Encrypted credentials
    api_key_encrypted = Column(Text, nullable=True)
    client_id = Column(String(100), nullable=True)  # Not encrypted as it's not sensitive
    password_encrypted = Column(Text, nullable=True)
    mpin_encrypted = Column(Text, nullable=True)
    totp_encrypted = Column(Text, nullable=True)
    
    # Connection status
    is_connected = Column(Boolean, default=False)
    last_connected = Column(DateTime(timezone=True), nullable=True)
    connection_error = Column(Text, nullable=True)
    
    # Session information (not stored for security)
    session_expiry = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="smartapi_credentials")
    
    def __repr__(self):
        return f"<SmartAPICredentials(user_id={self.user_id}, client_id='{self.client_id}', connected={self.is_connected})>"

class SmartAPISession(Base):
    __tablename__ = "smartapi_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Session tokens (encrypted)
    auth_token_encrypted = Column(Text, nullable=True)
    feed_token_encrypted = Column(Text, nullable=True)
    refresh_token_encrypted = Column(Text, nullable=True)
    
    # Session metadata
    session_start = Column(DateTime(timezone=True), server_default=func.now())
    session_expiry = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    
    # User info from Angel One
    user_name = Column(String(200), nullable=True)
    broker = Column(String(100), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="smartapi_sessions")
    
    def __repr__(self):
        return f"<SmartAPISession(user_id={self.user_id}, active={self.is_active}, expires={self.session_expiry})>"
