import React, { useState, useRef, useEffect } from 'react';
import './Header.css';

const Header = ({ 
  user, 
  onLogout, 
  isSmartAPIConnected, 
  connectionStatus, 
  userSubscription 
}) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications] = useState([
    {
      id: 1,
      type: 'signal',
      title: 'New Buy Signal',
      message: 'RELIANCE - Weekly High Strategy triggered',
      time: '2 minutes ago',
      unread: true
    },
    {
      id: 2,
      type: 'order',
      title: 'Order Executed',
      message: 'TCS buy order executed at ₹3,245',
      time: '15 minutes ago',
      unread: true
    },
    {
      id: 3,
      type: 'system',
      title: 'Market Update',
      message: 'Nifty 50 up 1.2% today',
      time: '1 hour ago',
      unread: false
    }
  ]);

  const userMenuRef = useRef(null);
  const notificationRef = useRef(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setShowUserMenu(false);
      }
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getConnectionStatusInfo = () => {
    if (isSmartAPIConnected) {
      return {
        status: 'connected',
        label: 'Connected',
        color: '#10b981',
        icon: '🟢'
      };
    } else if (connectionStatus === 'connecting') {
      return {
        status: 'connecting',
        label: 'Connecting...',
        color: '#f59e0b',
        icon: '🟡'
      };
    } else {
      return {
        status: 'disconnected',
        label: 'Disconnected',
        color: '#ef4444',
        icon: '🔴'
      };
    }
  };

  const connectionInfo = getConnectionStatusInfo();
  const unreadCount = notifications.filter(n => n.unread).length;

  const getSubscriptionBadge = () => {
    const badges = {
      'starter': { label: 'Starter', color: '#10b981' },
      'professional': { label: 'Pro', color: '#3b82f6' },
      'enterprise': { label: 'Enterprise', color: '#8b5cf6' }
    };
    return badges[userSubscription] || badges.starter;
  };

  const subscriptionBadge = getSubscriptionBadge();

  return (
    <header className="dashboard-header">
      <div className="header-left">
        <div className="page-title">
          <h1>Trading Dashboard</h1>
          <span className="page-subtitle">Real-time algorithmic trading platform</span>
        </div>
      </div>

      <div className="header-center">
        <div className="connection-status">
          <div 
            className={`status-indicator ${connectionInfo.status}`}
            title={`Angel One: ${connectionInfo.label}`}
          >
            <span className="status-icon">{connectionInfo.icon}</span>
            <span className="status-text">Angel One: {connectionInfo.label}</span>
          </div>
        </div>

        <div className="market-status">
          <div className="market-indicator">
            <span className="market-icon">📈</span>
            <div className="market-info">
              <span className="market-label">Nifty 50</span>
              <span className="market-value">19,245.30 (+1.2%)</span>
            </div>
          </div>
        </div>
      </div>

      <div className="header-right">
        {/* Notifications */}
        <div className="header-item" ref={notificationRef}>
          <button 
            className="notification-button"
            onClick={() => setShowNotifications(!showNotifications)}
            title="Notifications"
          >
            <span className="notification-icon">🔔</span>
            {unreadCount > 0 && (
              <span className="notification-badge">{unreadCount}</span>
            )}
          </button>

          {showNotifications && (
            <div className="notification-dropdown">
              <div className="dropdown-header">
                <h3>Notifications</h3>
                <button className="mark-all-read">Mark all read</button>
              </div>
              <div className="notification-list">
                {notifications.map(notification => (
                  <div 
                    key={notification.id} 
                    className={`notification-item ${notification.unread ? 'unread' : ''}`}
                  >
                    <div className="notification-content">
                      <div className="notification-header">
                        <span className="notification-title">{notification.title}</span>
                        <span className="notification-time">{notification.time}</span>
                      </div>
                      <p className="notification-message">{notification.message}</p>
                    </div>
                    {notification.unread && <div className="unread-dot"></div>}
                  </div>
                ))}
              </div>
              <div className="dropdown-footer">
                <button className="view-all-notifications">View All</button>
              </div>
            </div>
          )}
        </div>

        {/* User Menu */}
        <div className="header-item" ref={userMenuRef}>
          <button 
            className="user-button"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <div className="user-avatar">
              <span className="avatar-text">
                {user?.name?.charAt(0) || user?.username?.charAt(0) || 'U'}
              </span>
            </div>
            <div className="user-info">
              <span className="user-name">{user?.name || user?.username || 'User'}</span>
              <span 
                className="user-subscription"
                style={{ color: subscriptionBadge.color }}
              >
                {subscriptionBadge.label}
              </span>
            </div>
            <span className="dropdown-arrow">▼</span>
          </button>

          {showUserMenu && (
            <div className="user-dropdown">
              <div className="dropdown-header">
                <div className="user-profile">
                  <div className="user-avatar large">
                    <span className="avatar-text">
                      {user?.name?.charAt(0) || user?.username?.charAt(0) || 'U'}
                    </span>
                  </div>
                  <div className="user-details">
                    <span className="user-name">{user?.name || user?.username || 'User'}</span>
                    <span className="user-email">{user?.email || '<EMAIL>'}</span>
                    <span 
                      className="subscription-badge"
                      style={{ backgroundColor: subscriptionBadge.color }}
                    >
                      {subscriptionBadge.label} Plan
                    </span>
                  </div>
                </div>
              </div>

              <div className="dropdown-menu">
                <button className="menu-item">
                  <span className="menu-icon">👤</span>
                  Profile Settings
                </button>
                <button className="menu-item">
                  <span className="menu-icon">💳</span>
                  Billing & Subscription
                </button>
                <button className="menu-item">
                  <span className="menu-icon">🔔</span>
                  Notification Preferences
                </button>
                <button className="menu-item">
                  <span className="menu-icon">🔒</span>
                  Security Settings
                </button>
                <div className="menu-divider"></div>
                <button className="menu-item">
                  <span className="menu-icon">❓</span>
                  Help & Support
                </button>
                <button className="menu-item">
                  <span className="menu-icon">📚</span>
                  Documentation
                </button>
                <div className="menu-divider"></div>
                <button className="menu-item logout" onClick={onLogout}>
                  <span className="menu-icon">🚪</span>
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
