/* Weekly High Strategy Styles */
.weekly-high-strategy {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Strategy Header */
.strategy-header {
  background: linear-gradient(135deg, var(--success-50), var(--primary-50));
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--success-200);
}

.strategy-info h3 {
  margin: 0 0 var(--space-2);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
}

.strategy-info p {
  margin: 0 0 var(--space-4);
  color: var(--secondary-600);
  font-size: var(--text-base);
}

.signal-timing {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

/* Strategy Summary */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-4);
  background: var(--secondary-50);
  border-radius: var(--radius-xl);
  border: 1px solid var(--secondary-200);
}

.summary-label {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  margin-bottom: var(--space-2);
}

.summary-value {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
}

/* Strategy Table */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
}

.strategy-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.strategy-table thead {
  background: var(--secondary-50);
}

.strategy-table th {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  border-bottom: 2px solid var(--secondary-200);
  font-size: var(--text-sm);
}

.strategy-table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--secondary-100);
  font-size: var(--text-sm);
  color: var(--secondary-700);
}

.stock-row {
  transition: all var(--transition-base);
}

.stock-row:hover {
  background: var(--secondary-50);
}

/* Stock Info Cell */
.stock-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.stock-symbol {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  font-size: var(--text-sm);
}

.stock-name {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  line-height: 1.2;
}

/* Price Cells */
.price-cell {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  text-align: right;
}

.buy-price-cell {
  font-weight: var(--font-bold);
  color: var(--success-600);
  text-align: right;
}

.percentage-cell {
  font-weight: var(--font-semibold);
  text-align: right;
}

.percentage-cell.positive {
  color: var(--success-600);
}

.percentage-cell.negative {
  color: var(--error-600);
}

.quantity-cell {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  text-align: center;
}

.actions-cell {
  text-align: center;
}

/* Expanded OHLC Table */
.expanded-row {
  background: var(--secondary-25);
}

.expanded-row td {
  padding: 0;
}

.ohlc-table-container {
  padding: var(--space-6);
  background: var(--secondary-25);
}

.ohlc-table-container h4 {
  margin: 0 0 var(--space-4);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
}

.ohlc-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.ohlc-table thead {
  background: var(--primary-50);
}

.ohlc-table th {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--primary-900);
  border-bottom: 1px solid var(--primary-200);
  font-size: var(--text-xs);
}

.ohlc-table td {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--secondary-100);
  font-size: var(--text-xs);
  color: var(--secondary-700);
}

.ohlc-table .high-price {
  color: var(--success-600);
  font-weight: var(--font-semibold);
}

.ohlc-table .low-price {
  color: var(--error-600);
  font-weight: var(--font-semibold);
}

.ohlc-table .weekly-high {
  background: var(--success-50);
  color: var(--success-900);
  font-weight: var(--font-bold);
  text-align: center;
}

.ohlc-loading {
  padding: var(--space-4);
  text-align: center;
  color: var(--secondary-600);
}

/* Loading and Error States */
.weekly-high-loading,
.weekly-high-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--success-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: var(--text-4xl);
}

.error-content h3 {
  margin: 0;
  color: var(--error-600);
  font-size: var(--text-lg);
}

.error-content p {
  margin: 0;
  color: var(--secondary-600);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .signal-timing {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .strategy-header {
    padding: var(--space-4);
    text-align: center;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .strategy-table th,
  .strategy-table td {
    padding: var(--space-3) var(--space-4);
  }
  
  .stock-name {
    display: none;
  }
  
  .ohlc-table-container {
    padding: var(--space-4);
  }
}

@media (max-width: 640px) {
  .strategy-info h3 {
    font-size: var(--text-lg);
  }
  
  .strategy-table {
    font-size: var(--text-xs);
  }
  
  .strategy-table th,
  .strategy-table td {
    padding: var(--space-2) var(--space-3);
  }
  
  /* Hide less important columns on mobile */
  .strategy-table th:nth-child(3),
  .strategy-table td:nth-child(3),
  .strategy-table th:nth-child(5),
  .strategy-table td:nth-child(5) {
    display: none;
  }
}
