// Yahoo Finance API Service for real stock data
import axios from 'axios';

// Complete Nifty 200 stock symbols
const NIFTY_200_SYMBOLS = [
  // Nifty 50 stocks
  'RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'HINDUNILVR.NS',
  'ICICIBANK.NS', 'KOTAKBANK.NS', 'SBIN.NS', 'BHARTIARTL.NS', 'ITC.NS',
  'ASIANPAINT.NS', 'LT.NS', 'AXISBANK.NS', 'MARUTI.NS', 'SUNPHARMA.NS',
  'ULTRACEMCO.NS', 'TITAN.NS', 'WIPRO.NS', 'NESTLEIND.NS', 'POWERGRID.NS',
  'NTPC.NS', 'TECHM.NS', 'HCLTECH.NS', 'BAJFINANCE.NS', 'ONGC.NS',
  'TATAMOTORS.NS', 'COALINDIA.NS', 'ADANIPORTS.NS', 'JSWSTEEL.NS', 'GRASIM.NS',
  'HINDALCO.NS', 'INDUSINDBK.NS', 'BAJAJFINSV.NS', 'HEROMOTOCO.NS', 'CIPLA.NS',
  'DRREDDY.NS', 'EICHERMOT.NS', 'BRITANNIA.NS', 'BPCL.NS', 'DIVISLAB.NS',
  'APOLLOHOSP.NS', 'SHREECEM.NS', 'PIDILITIND.NS', 'DABUR.NS', 'GODREJCP.NS',
  'MARICO.NS', 'COLPAL.NS', 'BERGEPAINT.NS', 'VOLTAS.NS', 'HAVELLS.NS',

  // Additional Nifty Next 50 and Nifty 200 stocks
  'ADANIENT.NS', 'ADANIGREEN.NS', 'ADANITRANS.NS', 'AMBUJACEM.NS', 'BANDHANBNK.NS',
  'BIOCON.NS', 'BOSCHLTD.NS', 'CADILAHC.NS', 'CANBK.NS', 'CHOLAFIN.NS',
  'CONCOR.NS', 'COFORGE.NS', 'CROMPTON.NS', 'CUMMINSIND.NS', 'DEEPAKNTR.NS',
  'DMART.NS', 'ESCORTS.NS', 'EXIDEIND.NS', 'FEDERALBNK.NS', 'GAIL.NS',
  'GLENMARK.NS', 'GMRINFRA.NS', 'GODREJPROP.NS', 'GRANULES.NS', 'HDFCAMC.NS',
  'HDFCLIFE.NS', 'HONAUT.NS', 'IBULHSGFIN.NS', 'IDFCFIRSTB.NS', 'IEX.NS',
  'INDHOTEL.NS', 'INDUSTOWER.NS', 'IOC.NS', 'IRCTC.NS', 'JINDALSTEL.NS',
  'JUBLFOOD.NS', 'LICHSGFIN.NS', 'LUPIN.NS', 'M&M.NS', 'MANAPPURAM.NS',
  'MCDOWELL-N.NS', 'MINDTREE.NS', 'MOTHERSUMI.NS', 'MPHASIS.NS', 'MRF.NS',
  'MUTHOOTFIN.NS', 'NATIONALUM.NS', 'NAUKRI.NS', 'NAVINFLUOR.NS', 'NMDC.NS',
  'OBEROIRLTY.NS', 'OFSS.NS', 'OIL.NS', 'PAGEIND.NS', 'PEL.NS',
  'PERSISTENT.NS', 'PETRONET.NS', 'PFC.NS', 'PHOENIXLTD.NS', 'PIIND.NS',
  'PNB.NS', 'POLYCAB.NS', 'PVR.NS', 'RAMCOCEM.NS', 'RBLBANK.NS',
  'RECLTD.NS', 'SAIL.NS', 'SBICARD.NS', 'SBILIFE.NS', 'SIEMENS.NS',
  'SRF.NS', 'SRTRANSFIN.NS', 'STAR.NS', 'SUNTV.NS', 'TATACONSUM.NS',
  'TATAPOWER.NS', 'TATASTEEL.NS', 'TORNTPHARM.NS', 'TORNTPOWER.NS', 'TRENT.NS',
  'TVSMOTOR.NS', 'UBL.NS', 'UJJIVAN.NS', 'UPL.NS', 'VEDL.NS',
  'WHIRLPOOL.NS', 'YESBANK.NS', 'ZEEL.NS', 'ZYDUSLIFE.NS',

  // Additional stocks to complete Nifty 200
  'AARTIIND.NS', 'ABB.NS', 'ABBOTINDIA.NS', 'ABCAPITAL.NS', 'ABFRL.NS',
  'ACC.NS', 'AFFLE.NS', 'AJANTPHARM.NS', 'ALKEM.NS', 'AMARAJABAT.NS',
  'APLLTD.NS', 'ASHOKLEY.NS', 'ASTRAL.NS', 'ATUL.NS', 'AUBANK.NS',
  'AUROPHARMA.NS', 'BALKRISIND.NS', 'BALRAMCHIN.NS', 'BATAINDIA.NS', 'BEL.NS',
  'BHARATFORG.NS', 'BHEL.NS', 'BIOCON.NS', 'BLUEDART.NS', 'BSOFT.NS',
  'CANFINHOME.NS', 'CAPLIPOINT.NS', 'CARBORUNIV.NS', 'CASTROLIND.NS', 'CCL.NS',
  'CEATLTD.NS', 'CENTRALBK.NS', 'CENTURYTEX.NS', 'CERA.NS', 'CHALET.NS',
  'CHAMBLFERT.NS', 'CHOLAHLDNG.NS', 'CLEAN.NS', 'COALINDIA.NS', 'COCHINSHIP.NS',
  'COROMANDEL.NS', 'CREDITACC.NS', 'CRISIL.NS', 'CUB.NS', 'DELTACORP.NS',
  'DHANI.NS', 'DISHTV.NS', 'DLF.NS', 'DALBHARAT.NS', 'EMAMILTD.NS',
  'ENDURANCE.NS', 'ENGINERSIN.NS', 'EQUITAS.NS', 'ERIS.NS', 'FINEORG.NS',
  'FINPIPE.NS', 'FORCEMOT.NS', 'FORTIS.NS', 'FRETAIL.NS', 'GICRE.NS',
  'GILLETTE.NS', 'GLAXO.NS', 'GNFC.NS', 'GODFRYPHLP.NS', 'GPPL.NS',
  'GRAPHITE.NS', 'GSFC.NS', 'GSPL.NS', 'GTLINFRA.NS', 'GUJGASLTD.NS',
  'HAL.NS', 'HATSUN.NS', 'HCC.NS', 'HDFC.NS', 'HEIDELBERG.NS',
  'HEMIPROP.NS', 'HEXAWARE.NS', 'HFCL.NS', 'HIMATSEIDE.NS', 'HINDZINC.NS',
  'HSCL.NS', 'HUDCO.NS', 'ICICIPRULI.NS', 'IDEA.NS', 'IDFC.NS',
  'IFBIND.NS', 'IIFL.NS', 'INDIACEM.NS', 'INDIAMART.NS', 'INDIANB.NS',
  'INDIGO.NS', 'INDOCO.NS', 'INFIBEAM.NS', 'INOXLEISUR.NS', 'INTELLECT.NS',
  'IPCALAB.NS', 'IRB.NS', 'ISEC.NS', 'ITDC.NS', 'ITI.NS',
  'JAMNAAUTO.NS', 'JBCHEPHARM.NS', 'JCHAC.NS', 'JETAIRWAYS.NS', 'JKCEMENT.NS',
  'JKLAKSHMI.NS', 'JKPAPER.NS', 'JMFINANCIL.NS', 'JSL.NS', 'JSWENERGY.NS',
  'JUSTDIAL.NS', 'JYOTHYLAB.NS', 'KAJARIACER.NS', 'KANSAINER.NS', 'KEI.NS',
  'KNRCON.NS', 'KRBL.NS', 'L&TFH.NS', 'LALPATHLAB.NS', 'LAURUSLABS.NS',
  'LEMONTREE.NS', 'LINDEINDIA.NS', 'LTI.NS', 'LTTS.NS', 'LUXIND.NS',
  'LXCHEM.NS', 'MAHINDCIE.NS', 'MAHLIFE.NS', 'MAHLOG.NS', 'MAHSCOOTER.NS',
  'MAHSEAMLES.NS', 'MANINFRA.NS', 'MARICO.NS', 'MAXHEALTH.NS', 'METROPOLIS.NS',
  'MFSL.NS', 'MGL.NS', 'MIDHANI.NS', 'MOIL.NS', 'MOTILALOFS.NS',
  'MRPL.NS', 'MSUMI.NS', 'MTARTECH.NS', 'MUKANDLTD.NS', 'NATCOPHARM.NS',
  'NAUKRI.NS', 'NBCC.NS', 'NCC.NS', 'NESTLEIND.NS', 'NETWORK18.NS',
  'NHPC.NS', 'NIACL.NS', 'NIITLTD.NS', 'NLCINDIA.NS', 'NOCIL.NS'
];

// Yahoo Finance API endpoints
const YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';
const YAHOO_QUOTE_URL = 'https://query1.finance.yahoo.com/v7/finance/quote';

// Fetch stock data from Yahoo Finance
export const fetchStockData = async (symbol) => {
  try {
    const response = await axios.get(`${YAHOO_FINANCE_BASE_URL}/${symbol}`, {
      params: {
        range: '1y',
        interval: '1d',
        includePrePost: false,
        events: 'div,splits'
      }
    });

    const result = response.data.chart.result[0];
    const meta = result.meta;
    const timestamps = result.timestamp;
    const quotes = result.indicators.quote[0];

    // Calculate 52-week high and low with dates
    const { fiftyTwoWeekHigh, fiftyTwoWeekLow, fiftyTwoWeekHighDate, fiftyTwoWeekLowDate } = 
      calculate52WeekHighLow(timestamps, quotes.high, quotes.low);

    return {
      symbol: symbol.replace('.NS', ''),
      name: meta.longName || meta.shortName || symbol,
      currentPrice: meta.regularMarketPrice,
      previousClose: meta.previousClose,
      dayChange: meta.regularMarketPrice - meta.previousClose,
      dayChangePercent: ((meta.regularMarketPrice - meta.previousClose) / meta.previousClose) * 100,
      fiftyTwoWeekHigh,
      fiftyTwoWeekLow,
      fiftyTwoWeekHighDate,
      fiftyTwoWeekLowDate,
      volume: meta.regularMarketVolume,
      marketCap: meta.marketCap,
      timestamps,
      ohlcData: {
        open: quotes.open,
        high: quotes.high,
        low: quotes.low,
        close: quotes.close,
        volume: quotes.volume
      }
    };
  } catch (error) {
    console.error(`Error fetching data for ${symbol}:`, error);
    throw error;
  }
};

// Calculate 52-week high/low with exact dates
const calculate52WeekHighLow = (timestamps, highs, lows) => {
  let maxHigh = -Infinity;
  let minLow = Infinity;
  let maxHighIndex = -1;
  let minLowIndex = -1;

  for (let i = 0; i < highs.length; i++) {
    if (highs[i] !== null && highs[i] > maxHigh) {
      maxHigh = highs[i];
      maxHighIndex = i;
    }
    if (lows[i] !== null && lows[i] < minLow) {
      minLow = lows[i];
      minLowIndex = i;
    }
  }

  return {
    fiftyTwoWeekHigh: maxHigh,
    fiftyTwoWeekLow: minLow,
    fiftyTwoWeekHighDate: maxHighIndex >= 0 ? new Date(timestamps[maxHighIndex] * 1000).toISOString().split('T')[0] : null,
    fiftyTwoWeekLowDate: minLowIndex >= 0 ? new Date(timestamps[minLowIndex] * 1000).toISOString().split('T')[0] : null
  };
};

// Fetch Nifty 200 stock data
export const fetchNifty200StockData = async () => {
  try {
    const stockPromises = NIFTY_200_SYMBOLS.map(symbol => 
      fetchStockData(symbol).catch(error => {
        console.warn(`Failed to fetch ${symbol}:`, error);
        return null;
      })
    );

    const results = await Promise.all(stockPromises);
    return results.filter(stock => stock !== null);
  } catch (error) {
    console.error('Error fetching Nifty 200 data:', error);
    throw error;
  }
};

// Get weekly OHLC data for a specific stock
export const fetchWeeklyOHLCData = async (symbol, weeks = 4) => {
  try {
    const response = await axios.get(`${YAHOO_FINANCE_BASE_URL}/${symbol}.NS`, {
      params: {
        range: `${weeks * 7}d`,
        interval: '1d',
        includePrePost: false
      }
    });

    const result = response.data.chart.result[0];
    const timestamps = result.timestamp;
    const quotes = result.indicators.quote[0];

    // Group data by weeks (Monday to Friday)
    const weeklyData = groupDataByWeeks(timestamps, quotes);
    
    return weeklyData;
  } catch (error) {
    console.error(`Error fetching weekly data for ${symbol}:`, error);
    throw error;
  }
};

// Group daily data into weekly periods
const groupDataByWeeks = (timestamps, quotes) => {
  const weeks = [];
  let currentWeek = null;

  for (let i = 0; i < timestamps.length; i++) {
    const date = new Date(timestamps[i] * 1000);
    const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    
    // Start new week on Monday (dayOfWeek === 1) or if it's the first data point
    if (dayOfWeek === 1 || currentWeek === null) {
      if (currentWeek) {
        weeks.push(currentWeek);
      }
      currentWeek = {
        weekStart: date.toISOString().split('T')[0],
        weekEnd: null,
        days: [],
        weeklyHigh: -Infinity,
        weeklyLow: Infinity,
        weeklyOpen: quotes.open[i],
        weeklyClose: null
      };
    }

    // Add day data to current week
    if (currentWeek && dayOfWeek >= 1 && dayOfWeek <= 5) { // Monday to Friday
      const dayData = {
        date: date.toISOString().split('T')[0],
        dayName: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek],
        open: quotes.open[i],
        high: quotes.high[i],
        low: quotes.low[i],
        close: quotes.close[i],
        volume: quotes.volume[i]
      };

      currentWeek.days.push(dayData);
      
      // Update weekly high/low
      if (quotes.high[i] > currentWeek.weeklyHigh) {
        currentWeek.weeklyHigh = quotes.high[i];
      }
      if (quotes.low[i] < currentWeek.weeklyLow) {
        currentWeek.weeklyLow = quotes.low[i];
      }
      
      // Update weekly close (last trading day of the week)
      currentWeek.weeklyClose = quotes.close[i];
      currentWeek.weekEnd = date.toISOString().split('T')[0];
    }
  }

  // Add the last week if it exists
  if (currentWeek && currentWeek.days.length > 0) {
    weeks.push(currentWeek);
  }

  return weeks;
};

// Get last week's highest price for a stock
export const getLastWeekHighestPrice = async (symbol) => {
  try {
    const weeklyData = await fetchWeeklyOHLCData(symbol, 2); // Get last 2 weeks
    
    if (weeklyData.length >= 2) {
      // Return the second-to-last week's high (last complete week)
      return weeklyData[weeklyData.length - 2].weeklyHigh;
    } else if (weeklyData.length === 1) {
      // If only one week available, return its high
      return weeklyData[0].weeklyHigh;
    }
    
    return null;
  } catch (error) {
    console.error(`Error getting last week's high for ${symbol}:`, error);
    throw error;
  }
};

// Fetch real-time quote data
export const fetchRealTimeQuote = async (symbols) => {
  try {
    const symbolsWithSuffix = symbols.map(s => `${s}.NS`).join(',');
    
    const response = await axios.get(YAHOO_QUOTE_URL, {
      params: {
        symbols: symbolsWithSuffix
      }
    });

    return response.data.quoteResponse.result.map(quote => ({
      symbol: quote.symbol.replace('.NS', ''),
      currentPrice: quote.regularMarketPrice,
      previousClose: quote.regularMarketPreviousClose,
      dayChange: quote.regularMarketChange,
      dayChangePercent: quote.regularMarketChangePercent,
      volume: quote.regularMarketVolume,
      marketCap: quote.marketCap,
      fiftyTwoWeekHigh: quote.fiftyTwoWeekHigh,
      fiftyTwoWeekLow: quote.fiftyTwoWeekLow
    }));
  } catch (error) {
    console.error('Error fetching real-time quotes:', error);
    throw error;
  }
};

// Utility function to check if market is open
export const isMarketOpen = () => {
  const now = new Date();
  const istTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Kolkata"}));
  const day = istTime.getDay();
  const hour = istTime.getHours();
  const minute = istTime.getMinutes();
  const timeInMinutes = hour * 60 + minute;
  
  // Market is open Monday to Friday, 9:15 AM to 3:30 PM IST
  const marketOpen = 9 * 60 + 15; // 9:15 AM
  const marketClose = 15 * 60 + 30; // 3:30 PM
  
  return day >= 1 && day <= 5 && timeInMinutes >= marketOpen && timeInMinutes <= marketClose;
};

export default {
  fetchStockData,
  fetchNifty200StockData,
  fetchWeeklyOHLCData,
  getLastWeekHighestPrice,
  fetchRealTimeQuote,
  isMarketOpen
};
