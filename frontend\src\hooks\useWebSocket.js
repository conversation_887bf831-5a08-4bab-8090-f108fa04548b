import { useState, useEffect, useRef, useCallback } from 'react';

const useWebSocket = (userId, isSmartAPIConnected) => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastMessage, setLastMessage] = useState(null);
  const [marketData, setMarketData] = useState({});
  const [orderUpdates, setOrderUpdates] = useState([]);
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (!userId || !isSmartAPIConnected) {
      return;
    }

    try {
      const token = localStorage.getItem('auth_token');
      const wsUrl = `ws://localhost:8000/api/ws/${userId}?token=${token}`;
      
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;
        
        // Send ping to test connection
        sendMessage({
          type: 'ping',
          timestamp: new Date().toISOString()
        });
      };
      
      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          setLastMessage(message);
          
          // Handle different message types
          switch (message.type) {
            case 'market_data':
              setMarketData(prev => ({
                ...prev,
                [message.data.symbol]: {
                  ...message.data,
                  timestamp: message.timestamp
                }
              }));
              break;
              
            case 'order_update':
              setOrderUpdates(prev => [message.data, ...prev.slice(0, 49)]); // Keep last 50 updates
              break;
              
            case 'connection_status':
              console.log('Connection status:', message.status, message.message);
              break;
              
            case 'subscription_success':
              console.log('Subscribed to symbols:', message.symbols);
              break;
              
            case 'error':
              console.error('WebSocket error:', message.message);
              break;
              
            case 'pong':
              console.log('Pong received');
              break;
              
            case 'heartbeat':
              // Handle heartbeat
              break;
              
            default:
              console.log('Unknown message type:', message.type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      wsRef.current.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);
        setConnectionStatus('disconnected');
        
        // Attempt to reconnect if not manually closed
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const timeout = Math.pow(2, reconnectAttempts.current) * 1000; // Exponential backoff
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            console.log(`Reconnection attempt ${reconnectAttempts.current}`);
            connect();
          }, timeout);
        }
      };
      
      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };
      
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setConnectionStatus('error');
    }
  }, [userId, isSmartAPIConnected]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }
    
    setConnectionStatus('disconnected');
    reconnectAttempts.current = 0;
  }, []);

  const sendMessage = useCallback((message) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  const subscribeToSymbols = useCallback((symbols) => {
    return sendMessage({
      type: 'subscribe',
      symbols: symbols
    });
  }, [sendMessage]);

  const unsubscribeFromSymbols = useCallback((symbols) => {
    return sendMessage({
      type: 'unsubscribe',
      symbols: symbols
    });
  }, [sendMessage]);

  // Connect when SmartAPI is connected
  useEffect(() => {
    if (isSmartAPIConnected) {
      connect();
    } else {
      disconnect();
    }
    
    return () => {
      disconnect();
    };
  }, [isSmartAPIConnected, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    connectionStatus,
    lastMessage,
    marketData,
    orderUpdates,
    connect,
    disconnect,
    sendMessage,
    subscribeToSymbols,
    unsubscribeFromSymbols,
    isConnected: connectionStatus === 'connected'
  };
};

export default useWebSocket;
