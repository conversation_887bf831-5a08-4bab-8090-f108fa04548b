/* Modern Overview Page */
.overview-page {
  padding: 0;
  background: var(--secondary-50);
}

/* Modern Portfolio Summary Cards */
.portfolio-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.summary-card {
  position: relative;
  overflow: hidden;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--success-500));
  opacity: 0;
  transition: opacity var(--transition-base);
}

.summary-card:hover::before {
  opacity: 1;
}

.card-header-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.card-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
  border-radius: var(--radius-xl);
  font-size: var(--text-xl);
  border: 1px solid var(--primary-200);
}

.metric-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  margin: var(--space-4) 0 var(--space-2);
  line-height: var(--leading-none);
}

.metric-value.positive {
  color: var(--success-600);
}

.metric-value.negative {
  color: var(--error-600);
}

.change-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.change-indicator.positive {
  color: var(--success-600);
}

.change-indicator.negative {
  color: var(--error-600);
}

.change-indicator.neutral {
  color: var(--secondary-500);
}

.change-icon {
  font-size: var(--text-base);
  font-weight: var(--font-bold);
}

.status-text {
  color: var(--secondary-600);
  font-weight: var(--font-medium);
}

/* Overview Grid */
.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.overview-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f4f6;
}

.section-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
}

.view-all-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.view-all-btn:hover {
  color: #1e40af;
}

.market-time {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.market-time::before {
  content: '●';
  color: #10b981;
  animation: pulse 2s infinite;
}

/* Recent Signals */
.signals-list {
  padding: 0 1.5rem 1.5rem;
}

.signal-item {
  padding: 1rem;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
}

.signal-item:last-child {
  margin-bottom: 0;
}

.signal-item:hover {
  border-color: #e5e7eb;
  background: #f8fafc;
}

.signal-item.locked {
  opacity: 0.6;
  background: #f9fafb;
}

.signal-main {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.signal-strategy {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.signal-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.signal-details {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.signal-symbol {
  font-weight: 700;
  color: #1a1a1a;
  font-size: 1rem;
}

.signal-action {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
}

.signal-action.buy {
  background: #dcfce7;
  color: #166534;
}

.signal-action.sell {
  background: #fee2e2;
  color: #991b1b;
}

.signal-price {
  font-weight: 600;
  color: #374151;
}

.signal-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #9ca3af;
}

.signal-confidence {
  font-weight: 500;
}

.lock-icon {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 1rem;
  color: #9ca3af;
}

/* Active Strategies */
.strategies-list {
  padding: 0 1.5rem 1.5rem;
}

.strategy-item {
  padding: 1rem;
  border: 1px solid #f3f4f6;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  position: relative;
  transition: all 0.3s ease;
}

.strategy-item:last-child {
  margin-bottom: 0;
}

.strategy-item:hover {
  border-color: #e5e7eb;
  background: #f8fafc;
}

.strategy-item.locked {
  opacity: 0.6;
  background: #f9fafb;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.strategy-name {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.strategy-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.strategy-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.metric-value {
  font-weight: 700;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.metric-value.positive {
  color: #10b981;
}

.strategy-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  gap: 0.5rem;
}

.upgrade-text {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* Market Overview */
.market-list {
  padding: 0 1.5rem 1.5rem;
}

.market-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.market-item:last-child {
  border-bottom: none;
}

.market-info {
  display: flex;
  flex-direction: column;
}

.market-symbol {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.market-volume {
  font-size: 0.75rem;
  color: #6b7280;
}

.market-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price-value {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.price-change {
  font-size: 0.75rem;
  font-weight: 500;
}

.price-change.positive {
  color: #10b981;
}

.price-change.negative {
  color: #ef4444;
}

/* Quick Actions */
.quick-actions {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.quick-actions h2 {
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 2rem;
}

.action-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .portfolio-summary {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .portfolio-summary {
    grid-template-columns: 1fr;
  }
  
  .strategy-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .signal-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
