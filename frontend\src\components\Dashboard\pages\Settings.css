/* Modern Settings Page */
.settings-page {
  padding: 0;
  background: var(--secondary-50);
}

.settings-container {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--space-8);
  min-height: calc(100vh - 200px);
}

/* Modern Settings Navigation */
.settings-nav {
  background: white;
  border-radius: var(--radius-2xl);
  border: 1px solid var(--secondary-200);
  padding: var(--space-4);
  height: fit-content;
  position: sticky;
  top: var(--space-8);
  box-shadow: var(--shadow-lg);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: none;
  border: none;
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-base);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--secondary-600);
  text-align: left;
  margin-bottom: var(--space-2);
  position: relative;
}

.nav-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
  color: var(--primary-700);
  border: 1px solid var(--primary-200);
  box-shadow: var(--shadow-sm);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--primary-600);
  border-radius: 0 var(--radius-base) var(--radius-base) 0;
}

.nav-icon {
  font-size: var(--text-xl);
}

/* Modern Settings Content */
.settings-content {
  background: white;
  border-radius: var(--radius-2xl);
  border: 1px solid var(--secondary-200);
  padding: var(--space-8);
  box-shadow: var(--shadow-lg);
}

.settings-section h2 {
  margin: 0 0 var(--space-2);
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  line-height: var(--leading-tight);
}

.settings-section > p {
  margin: 0 0 var(--space-8);
  color: var(--secondary-600);
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  line-height: var(--leading-relaxed);
}

/* Settings Form */
.settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.settings-input, .settings-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.3s ease;
}

.settings-input:focus, .settings-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.settings-input.small {
  width: 100px;
}

/* Toggle Controls */
.toggle-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.toggle-label input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background: #d1d5db;
  border-radius: 12px;
  transition: background-color 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider {
  background: #3b82f6;
}

.toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.sub-setting {
  margin-left: 3rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sub-setting label {
  font-size: 0.8rem;
  color: #6b7280;
  min-width: 80px;
}

/* Notification Groups */
.notification-group {
  margin-bottom: 2rem;
}

.notification-group h3 {
  margin: 0 0 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

/* Buttons */
.save-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.secondary-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.secondary-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.danger-btn {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.danger-btn:hover {
  background: #fecaca;
  border-color: #f87171;
}

.upgrade-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upgrade-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Security Actions */
.security-actions {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

/* Subscription Info */
.subscription-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.current-plan {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.current-plan h3 {
  margin: 0 0 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.plan-details {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.plan-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.plan-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.plan-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #374151;
}

.plan-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.billing-info {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.billing-info h3 {
  margin: 0 0 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.billing-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.billing-details p {
  margin: 0;
  font-size: 0.875rem;
  color: #374151;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .settings-container {
    grid-template-columns: 1fr;
  }
  
  .settings-nav {
    position: static;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
  }
  
  .nav-item {
    margin-bottom: 0;
    justify-content: center;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .settings-content {
    padding: 1.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .settings-nav {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .nav-item {
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.75rem 0.5rem;
  }
  
  .nav-icon {
    font-size: 1.5rem;
  }
  
  .security-actions,
  .plan-actions {
    flex-direction: column;
  }
  
  .plan-details {
    flex-direction: column;
    align-items: flex-start;
  }
}
