/* Dashboard Header */
.dashboard-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  min-height: 80px;
}

.header-left {
  flex: 1;
}

.page-title h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
}

.page-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #e5e7eb;
}

.status-indicator.connected {
  background: #dcfce7;
  border-color: #bbf7d0;
  color: #166534;
}

.status-indicator.connecting {
  background: #fef3c7;
  border-color: #fde68a;
  color: #92400e;
}

.status-indicator.disconnected {
  background: #fee2e2;
  border-color: #fecaca;
  color: #991b1b;
}

.status-icon {
  font-size: 0.75rem;
}

.market-status {
  display: flex;
  align-items: center;
}

.market-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.market-icon {
  font-size: 1rem;
}

.market-info {
  display: flex;
  flex-direction: column;
}

.market-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.market-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #10b981;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-item {
  position: relative;
}

/* Notifications */
.notification-button {
  position: relative;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notification-button:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.notification-icon {
  font-size: 1.25rem;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 350px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.5rem;
}

.dropdown-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.mark-all-read {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.875rem;
  cursor: pointer;
  font-weight: 500;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: #f8fafc;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.25rem;
}

.notification-title {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.notification-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

.notification-message {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  margin-top: 0.25rem;
}

.dropdown-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.view-all-notifications {
  background: none;
  border: none;
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  font-size: 0.875rem;
}

/* User Menu */
.user-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-button:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-avatar.large {
  width: 48px;
  height: 48px;
  font-size: 1.25rem;
}

.avatar-text {
  text-transform: uppercase;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.user-subscription {
  font-size: 0.75rem;
  font-weight: 500;
}

.user-email {
  font-size: 0.75rem;
  color: #6b7280;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: #9ca3af;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.5rem;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dropdown-menu {
  padding: 0.5rem 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.875rem;
  color: #374151;
}

.menu-item:hover {
  background: #f3f4f6;
}

.menu-item.logout {
  color: #ef4444;
}

.menu-item.logout:hover {
  background: #fef2f2;
}

.menu-icon {
  font-size: 1rem;
  width: 20px;
  text-align: center;
}

.menu-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-header {
    padding: 1rem;
  }
  
  .header-center {
    display: none;
  }
  
  .notification-dropdown,
  .user-dropdown {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 0.75rem 1rem;
  }
  
  .page-title h1 {
    font-size: 1.25rem;
  }
  
  .header-right {
    gap: 0.5rem;
  }
  
  .user-info {
    display: none;
  }
  
  .notification-dropdown,
  .user-dropdown {
    width: 280px;
    right: -50px;
  }
}
