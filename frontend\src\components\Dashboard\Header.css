/* Modern Dashboard Header */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--secondary-200);
  padding: 0 var(--space-8);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  height: var(--header-height);
  box-shadow: var(--shadow-sm);
}

.header-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.page-title h1 {
  margin: 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  line-height: var(--leading-tight);
}

.page-subtitle {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: var(--font-medium);
  margin: 0;
}

.header-center {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  border: 1px solid transparent;
  transition: all var(--transition-base);
}

.status-indicator.connected {
  background: var(--success-100);
  border-color: var(--success-200);
  color: var(--success-700);
  box-shadow: var(--shadow-sm);
}

.status-indicator.connecting {
  background: var(--warning-100);
  border-color: var(--warning-200);
  color: var(--warning-700);
  box-shadow: var(--shadow-sm);
}

.status-indicator.disconnected {
  background: var(--error-100);
  border-color: var(--error-200);
  color: var(--error-700);
  box-shadow: var(--shadow-sm);
}

.status-icon {
  font-size: var(--text-sm);
}

.market-status {
  display: flex;
  align-items: center;
}

.market-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.market-icon {
  font-size: 1rem;
}

.market-info {
  display: flex;
  flex-direction: column;
}

.market-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.market-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #10b981;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-item {
  position: relative;
}

/* Modern Notifications */
.notification-button {
  position: relative;
  background: var(--secondary-100);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-xl);
  padding: var(--space-3);
  cursor: pointer;
  transition: all var(--transition-base);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-button:hover {
  background: var(--secondary-200);
  border-color: var(--secondary-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.notification-icon {
  font-size: var(--text-xl);
  color: var(--secondary-600);
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--error-500);
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 350px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.5rem;
}

.dropdown-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.mark-all-read {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.875rem;
  cursor: pointer;
  font-weight: 500;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: #f8fafc;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.25rem;
}

.notification-title {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.notification-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

.notification-message {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  margin-top: 0.25rem;
}

.dropdown-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.view-all-notifications {
  background: none;
  border: none;
  color: #3b82f6;
  font-weight: 500;
  cursor: pointer;
  font-size: 0.875rem;
}

/* User Menu */
.user-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-button:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-avatar.large {
  width: 48px;
  height: 48px;
  font-size: 1.25rem;
}

.avatar-text {
  text-transform: uppercase;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.user-subscription {
  font-size: 0.75rem;
  font-weight: 500;
}

.user-email {
  font-size: 0.75rem;
  color: #6b7280;
}

.dropdown-arrow {
  font-size: 0.75rem;
  color: #9ca3af;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 280px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.5rem;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dropdown-menu {
  padding: 0.5rem 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.875rem;
  color: #374151;
}

.menu-item:hover {
  background: #f3f4f6;
}

.menu-item.logout {
  color: #ef4444;
}

.menu-item.logout:hover {
  background: #fef2f2;
}

.menu-icon {
  font-size: 1rem;
  width: 20px;
  text-align: center;
}

.menu-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-header {
    padding: 1rem;
  }
  
  .header-center {
    display: none;
  }
  
  .notification-dropdown,
  .user-dropdown {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 0.75rem 1rem;
  }
  
  .page-title h1 {
    font-size: 1.25rem;
  }
  
  .header-right {
    gap: 0.5rem;
  }
  
  .user-info {
    display: none;
  }
  
  .notification-dropdown,
  .user-dropdown {
    width: 280px;
    right: -50px;
  }
}
