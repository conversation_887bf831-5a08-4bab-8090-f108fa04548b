from fastapi import APIRouter, HTT<PERSON><PERSON>xception, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from sqlalchemy.orm import Session
from database.database import get_db
from services.smartapi_service import smartapi_manager
from services.credential_service import credential_service
from typing import Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()

def get_current_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Extract user ID from JWT token - simplified for demo"""
    return "user_123"  # Mock user ID

class OrderRequest(BaseModel):
    symbol: str
    quantity: int
    order_type: str  # "BUY" or "SELL"
    price_type: str  # "MARKET" or "LIMIT"
    price: Optional[float] = None
    exchange: Optional[str] = "NSE"
    product_type: Optional[str] = "INTRADAY"

class OrderResponse(BaseModel):
    order_id: str
    status: str
    message: str

@router.post("/place-order", response_model=OrderResponse)
async def place_order(
    order: OrderRequest,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Place a trading order via SmartAPI"""
    try:
        # Get SmartAPI service for user
        smartapi_service = smartapi_manager.get_service(user_id)

        # Check if user is connected
        if not smartapi_service.is_connected:
            # Try to restore session
            active_session = credential_service.get_active_session(db, user_id)
            if active_session:
                smartapi_service.auth_token = active_session.get('auth_token')
                smartapi_service.feed_token = active_session.get('feed_token')
                smartapi_service.refresh_token = active_session.get('refresh_token')
                smartapi_service.session_expiry = active_session.get('session_expiry')
                smartapi_service.is_connected = True
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Not connected to Angel One. Please connect first."
                )

        # Place order through SmartAPI
        result = await smartapi_service.place_order(order.dict())

        if result["success"]:
            return OrderResponse(
                order_id=result["order_id"],
                status="PENDING",
                message=result["message"]
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error placing order: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error placing order: {str(e)}"
        )

@router.get("/orders")
async def get_orders(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get all orders for the user"""
    try:
        # Get SmartAPI service for user
        smartapi_service = smartapi_manager.get_service(user_id)

        # Check if user is connected
        if not smartapi_service.is_connected:
            # Try to restore session
            active_session = credential_service.get_active_session(db, user_id)
            if active_session:
                smartapi_service.auth_token = active_session.get('auth_token')
                smartapi_service.feed_token = active_session.get('feed_token')
                smartapi_service.refresh_token = active_session.get('refresh_token')
                smartapi_service.session_expiry = active_session.get('session_expiry')
                smartapi_service.is_connected = True
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Not connected to Angel One. Please connect first."
                )

        # Get orders from SmartAPI
        result = await smartapi_service.get_orders()

        if result["success"]:
            return {"orders": result["orders"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching orders: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching orders"
        )

@router.get("/positions")
async def get_positions(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get current positions"""
    try:
        # Get SmartAPI service for user
        smartapi_service = smartapi_manager.get_service(user_id)

        # Check if user is connected
        if not smartapi_service.is_connected:
            # Try to restore session
            active_session = credential_service.get_active_session(db, user_id)
            if active_session:
                smartapi_service.auth_token = active_session.get('auth_token')
                smartapi_service.feed_token = active_session.get('feed_token')
                smartapi_service.refresh_token = active_session.get('refresh_token')
                smartapi_service.session_expiry = active_session.get('session_expiry')
                smartapi_service.is_connected = True
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Not connected to Angel One. Please connect first."
                )

        # Get positions from SmartAPI
        result = await smartapi_service.get_positions()

        if result["success"]:
            return {"positions": result["positions"]}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching positions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching positions"
        )

@router.delete("/order/{order_id}")
async def cancel_order(order_id: str):
    """Cancel an existing order"""
    try:
        logger.info(f"Cancelling order: {order_id}")
        
        # Mock cancellation - replace with actual SmartAPI integration
        return {
            "order_id": order_id,
            "status": "CANCELLED",
            "message": "Order cancelled successfully"
        }
        
    except Exception as e:
        logger.error(f"Error cancelling order {order_id}: {e}")
        raise HTTPException(status_code=400, detail=f"Error cancelling order: {str(e)}")
