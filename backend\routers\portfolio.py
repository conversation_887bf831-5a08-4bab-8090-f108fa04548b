from fastapi import APIRouter, HTTPException
from typing import List, Dict
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/summary")
async def get_portfolio_summary():
    """Get portfolio summary"""
    try:
        # Mock portfolio data - replace with actual database queries
        return {
            "total_value": 50000.00,
            "total_invested": 45000.00,
            "total_pnl": 5000.00,
            "total_pnl_percent": 11.11,
            "day_pnl": 250.00,
            "day_pnl_percent": 0.50,
            "cash_balance": 10000.00,
            "buying_power": 20000.00
        }
    except Exception as e:
        logger.error(f"Error fetching portfolio summary: {e}")
        raise HTTPException(status_code=500, detail="Error fetching portfolio summary")

@router.get("/holdings")
async def get_holdings():
    """Get all portfolio holdings"""
    try:
        # Mock holdings data
        return {
            "holdings": [
                {
                    "symbol": "AAPL",
                    "name": "Apple Inc.",
                    "quantity": 10,
                    "avg_price": 150.00,
                    "current_price": 155.00,
                    "market_value": 1550.00,
                    "invested_value": 1500.00,
                    "pnl": 50.00,
                    "pnl_percent": 3.33,
                    "day_change": 5.00,
                    "day_change_percent": 3.33
                },
                {
                    "symbol": "GOOGL",
                    "name": "Alphabet Inc.",
                    "quantity": 5,
                    "avg_price": 2800.00,
                    "current_price": 2850.00,
                    "market_value": 14250.00,
                    "invested_value": 14000.00,
                    "pnl": 250.00,
                    "pnl_percent": 1.79,
                    "day_change": 25.00,
                    "day_change_percent": 0.88
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error fetching holdings: {e}")
        raise HTTPException(status_code=500, detail="Error fetching holdings")

@router.get("/performance")
async def get_performance():
    """Get portfolio performance metrics"""
    try:
        # Mock performance data
        return {
            "performance": {
                "1d": {"return": 0.50, "value": 50250.00},
                "1w": {"return": 2.30, "value": 51150.00},
                "1m": {"return": 8.50, "value": 54250.00},
                "3m": {"return": 15.20, "value": 57600.00},
                "1y": {"return": 22.40, "value": 61200.00}
            },
            "benchmark": {
                "name": "S&P 500",
                "symbol": "SPY",
                "1d": 0.30,
                "1w": 1.80,
                "1m": 6.20,
                "3m": 12.50,
                "1y": 18.90
            }
        }
    except Exception as e:
        logger.error(f"Error fetching performance: {e}")
        raise HTTPException(status_code=500, detail="Error fetching performance")

@router.get("/watchlist")
async def get_watchlist():
    """Get user's watchlist"""
    try:
        # Mock watchlist data
        return {
            "watchlist": [
                {
                    "symbol": "TSLA",
                    "name": "Tesla Inc.",
                    "price": 250.00,
                    "change": 5.00,
                    "change_percent": 2.04
                },
                {
                    "symbol": "MSFT",
                    "name": "Microsoft Corporation",
                    "price": 380.00,
                    "change": -2.50,
                    "change_percent": -0.65
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error fetching watchlist: {e}")
        raise HTTPException(status_code=500, detail="Error fetching watchlist")
