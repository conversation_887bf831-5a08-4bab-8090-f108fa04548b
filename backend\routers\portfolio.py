from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from database.database import get_db
from services.smartapi_service import smartapi_manager
from services.credential_service import credential_service
from typing import List, Dict
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()

def get_current_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Extract user ID from JWT token - simplified for demo"""
    return "user_123"  # Mock user ID

@router.get("/summary")
async def get_portfolio_summary():
    """Get portfolio summary"""
    try:
        # Mock portfolio data - replace with actual database queries
        return {
            "total_value": 50000.00,
            "total_invested": 45000.00,
            "total_pnl": 5000.00,
            "total_pnl_percent": 11.11,
            "day_pnl": 250.00,
            "day_pnl_percent": 0.50,
            "cash_balance": 10000.00,
            "buying_power": 20000.00
        }
    except Exception as e:
        logger.error(f"Error fetching portfolio summary: {e}")
        raise HTTPException(status_code=500, detail="Error fetching portfolio summary")

@router.get("/holdings")
async def get_holdings(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get all portfolio holdings"""
    try:
        # Get SmartAPI service for user
        smartapi_service = smartapi_manager.get_service(user_id)

        # Check if user is connected
        if not smartapi_service.is_connected:
            # Try to restore session
            active_session = credential_service.get_active_session(db, user_id)
            if active_session:
                smartapi_service.auth_token = active_session.get('auth_token')
                smartapi_service.feed_token = active_session.get('feed_token')
                smartapi_service.refresh_token = active_session.get('refresh_token')
                smartapi_service.session_expiry = active_session.get('session_expiry')
                smartapi_service.is_connected = True
            else:
                # Return empty holdings if not connected
                return {"holdings": [], "summary": {
                    "total_invested": 0,
                    "total_current": 0,
                    "total_pnl": 0,
                    "total_pnl_percent": 0
                }}

        # Get holdings from SmartAPI
        result = await smartapi_service.get_holdings()

        if result["success"]:
            return {
                "holdings": result["holdings"],
                "summary": result["summary"]
            }
        else:
            logger.error(f"Error fetching holdings: {result['error']}")
            return {"holdings": [], "summary": {
                "total_invested": 0,
                "total_current": 0,
                "total_pnl": 0,
                "total_pnl_percent": 0
            }}

    except Exception as e:
        logger.error(f"Error fetching holdings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching holdings"
        )

@router.get("/performance")
async def get_performance():
    """Get portfolio performance metrics"""
    try:
        # Mock performance data
        return {
            "performance": {
                "1d": {"return": 0.50, "value": 50250.00},
                "1w": {"return": 2.30, "value": 51150.00},
                "1m": {"return": 8.50, "value": 54250.00},
                "3m": {"return": 15.20, "value": 57600.00},
                "1y": {"return": 22.40, "value": 61200.00}
            },
            "benchmark": {
                "name": "S&P 500",
                "symbol": "SPY",
                "1d": 0.30,
                "1w": 1.80,
                "1m": 6.20,
                "3m": 12.50,
                "1y": 18.90
            }
        }
    except Exception as e:
        logger.error(f"Error fetching performance: {e}")
        raise HTTPException(status_code=500, detail="Error fetching performance")

@router.get("/watchlist")
async def get_watchlist():
    """Get user's watchlist"""
    try:
        # Mock watchlist data
        return {
            "watchlist": [
                {
                    "symbol": "TSLA",
                    "name": "Tesla Inc.",
                    "price": 250.00,
                    "change": 5.00,
                    "change_percent": 2.04
                },
                {
                    "symbol": "MSFT",
                    "name": "Microsoft Corporation",
                    "price": 380.00,
                    "change": -2.50,
                    "change_percent": -0.65
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error fetching watchlist: {e}")
        raise HTTPException(status_code=500, detail="Error fetching watchlist")
