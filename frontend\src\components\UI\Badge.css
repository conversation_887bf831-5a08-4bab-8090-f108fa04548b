/* Badge Component */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-family-sans);
  font-weight: var(--font-medium);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border: 1px solid transparent;
  transition: all var(--transition-base);
}

/* Badge Sizes */
.badge--xs {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  line-height: var(--leading-none);
}

.badge--sm {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  line-height: var(--leading-tight);
}

.badge--md {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  line-height: var(--leading-tight);
}

.badge--lg {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

/* Badge Shapes */
.badge--rounded {
  border-radius: var(--radius-md);
}

.badge--pill {
  border-radius: var(--radius-full);
}

.badge--square {
  border-radius: var(--radius-base);
}

/* Badge Variants */
.badge--default {
  background: var(--secondary-100);
  color: var(--secondary-700);
  border-color: var(--secondary-200);
}

.badge--primary {
  background: var(--primary-100);
  color: var(--primary-700);
  border-color: var(--primary-200);
}

.badge--secondary {
  background: var(--secondary-100);
  color: var(--secondary-700);
  border-color: var(--secondary-200);
}

.badge--success {
  background: var(--success-100);
  color: var(--success-700);
  border-color: var(--success-200);
}

.badge--warning {
  background: var(--warning-100);
  color: var(--warning-700);
  border-color: var(--warning-200);
}

.badge--error {
  background: var(--error-100);
  color: var(--error-700);
  border-color: var(--error-200);
}

/* Solid Variants */
.badge--primary-solid {
  background: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.badge--secondary-solid {
  background: var(--secondary-600);
  color: white;
  border-color: var(--secondary-600);
}

.badge--success-solid {
  background: var(--success-600);
  color: white;
  border-color: var(--success-600);
}

.badge--warning-solid {
  background: var(--warning-600);
  color: white;
  border-color: var(--warning-600);
}

.badge--error-solid {
  background: var(--error-600);
  color: white;
  border-color: var(--error-600);
}

/* Outline Variants */
.badge--primary-outline {
  background: transparent;
  color: var(--primary-600);
  border-color: var(--primary-300);
}

.badge--secondary-outline {
  background: transparent;
  color: var(--secondary-600);
  border-color: var(--secondary-300);
}

.badge--success-outline {
  background: transparent;
  color: var(--success-600);
  border-color: var(--success-300);
}

.badge--warning-outline {
  background: transparent;
  color: var(--warning-600);
  border-color: var(--warning-300);
}

.badge--error-outline {
  background: transparent;
  color: var(--error-600);
  border-color: var(--error-300);
}

/* Special Variants */
.badge--gradient {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border-color: transparent;
  box-shadow: var(--shadow-sm);
}

.badge--glass {
  background: rgba(255, 255, 255, 0.1);
  color: var(--secondary-700);
  border-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* Status Badges */
.badge--active {
  background: var(--success-100);
  color: var(--success-700);
  border-color: var(--success-200);
}

.badge--inactive {
  background: var(--secondary-100);
  color: var(--secondary-500);
  border-color: var(--secondary-200);
}

.badge--pending {
  background: var(--warning-100);
  color: var(--warning-700);
  border-color: var(--warning-200);
}

.badge--completed {
  background: var(--success-100);
  color: var(--success-700);
  border-color: var(--success-200);
}

.badge--failed {
  background: var(--error-100);
  color: var(--error-700);
  border-color: var(--error-200);
}

/* Dot Badges */
.badge--dot {
  position: relative;
  padding-left: var(--space-5);
}

.badge--dot::before {
  content: '';
  position: absolute;
  left: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* Responsive Design */
@media (max-width: 640px) {
  .badge--xs {
    padding: 1px var(--space-1);
    font-size: 0.625rem;
  }
  
  .badge--sm {
    padding: var(--space-1) var(--space-2);
    font-size: 0.7rem;
  }
  
  .badge--md {
    padding: var(--space-1) var(--space-2);
    font-size: var(--text-xs);
  }
  
  .badge--lg {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-sm);
  }
}
