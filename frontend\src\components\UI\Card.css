/* Card Component */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  background: white;
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-xl);
  transition: all var(--transition-base);
  overflow: hidden;
}

/* Card Variants */
.card--default {
  background: white;
  border-color: var(--secondary-200);
}

.card--elevated {
  background: white;
  border-color: transparent;
  box-shadow: var(--shadow-lg);
}

.card--outlined {
  background: white;
  border-color: var(--secondary-300);
  border-width: 2px;
}

.card--filled {
  background: var(--secondary-50);
  border-color: var(--secondary-200);
}

.card--gradient {
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
  border-color: var(--primary-200);
}

.card--success {
  background: var(--success-50);
  border-color: var(--success-200);
}

.card--warning {
  background: var(--warning-50);
  border-color: var(--warning-200);
}

.card--error {
  background: var(--error-50);
  border-color: var(--error-200);
}

/* Card Padding */
.card--padding-none {
  padding: 0;
}

.card--padding-sm {
  padding: var(--space-4);
}

.card--padding-default {
  padding: var(--card-padding);
}

.card--padding-lg {
  padding: var(--space-8);
}

.card--padding-xl {
  padding: var(--space-10);
}

/* Card Shadows */
.card--shadow-none {
  box-shadow: none;
}

.card--shadow-sm {
  box-shadow: var(--shadow-sm);
}

.card--shadow-default {
  box-shadow: var(--shadow-base);
}

.card--shadow-md {
  box-shadow: var(--shadow-md);
}

.card--shadow-lg {
  box-shadow: var(--shadow-lg);
}

.card--shadow-xl {
  box-shadow: var(--shadow-xl);
}

/* Interactive States */
.card--hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.card--clickable {
  cursor: pointer;
  border: none;
  text-align: left;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

.card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.card--clickable:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

.card--clickable:active {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Card Elements */
.card__header {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.card__title {
  margin: 0;
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  color: var(--secondary-900);
}

.card__subtitle {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  line-height: var(--leading-normal);
  color: var(--secondary-600);
}

.card__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.card__footer {
  margin-top: var(--space-6);
  padding-top: var(--space-4);
  border-top: 1px solid var(--secondary-200);
}

.card__actions {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-4);
}

.card__actions--left {
  justify-content: flex-start;
}

.card__actions--center {
  justify-content: center;
}

.card__actions--right {
  justify-content: flex-end;
}

.card__actions--between {
  justify-content: space-between;
}

/* Card with no padding for custom layouts */
.card--padding-none .card__header,
.card--padding-none .card__content,
.card--padding-none .card__footer {
  padding: var(--card-padding);
}

.card--padding-none .card__header {
  padding-bottom: 0;
}

.card--padding-none .card__footer {
  padding-top: 0;
}

/* Special Card Types */
.card--metric {
  text-align: center;
  padding: var(--space-6);
}

.card--metric .card__title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--primary-600);
  margin-bottom: var(--space-2);
}

.card--metric .card__subtitle {
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--secondary-500);
}

.card--stat {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-5);
}

.card--stat .card__icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--radius-xl);
  color: var(--primary-600);
  font-size: var(--text-xl);
}

.card--stat .card__content {
  flex: 1;
  margin: 0;
}

.card--stat .card__title {
  font-size: var(--text-2xl);
  margin-bottom: var(--space-1);
}

.card--stat .card__subtitle {
  font-size: var(--text-sm);
}

/* Loading State */
.card--loading {
  position: relative;
  overflow: hidden;
}

.card--loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: card-shimmer 1.5s infinite;
}

@keyframes card-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .card--padding-default {
    padding: var(--space-4);
  }
  
  .card--padding-lg {
    padding: var(--space-6);
  }
  
  .card--padding-xl {
    padding: var(--space-8);
  }
  
  .card__title {
    font-size: var(--text-lg);
  }
  
  .card__actions {
    flex-direction: column;
    gap: var(--space-2);
  }
  
  .card__actions--right,
  .card__actions--left,
  .card__actions--center {
    justify-content: stretch;
  }
  
  .card--metric {
    padding: var(--space-4);
  }
  
  .card--metric .card__title {
    font-size: var(--text-2xl);
  }
  
  .card--stat {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
}
