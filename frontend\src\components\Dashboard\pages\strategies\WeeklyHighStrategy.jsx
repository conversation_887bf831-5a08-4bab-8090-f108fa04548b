import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Badge, PageHeader } from '../../../UI';
import './WeeklyHighStrategy.css';

const WeeklyHighStrategy = ({ 
  isSmartAPIConnected, 
  marketData, 
  subscribeToSymbols,
  unsubscribeFromSymbols 
}) => {
  const [activeTab, setActiveTab] = useState('signals');
  const [signals, setSignals] = useState([
    {
      id: 1,
      symbol: 'RELIANCE',
      companyName: 'Reliance Industries Ltd',
      action: 'BUY',
      triggerPrice: 2456.75,
      currentPrice: 2445.30,
      weeklyHigh: 2456.75,
      confidence: 85,
      volume: 1250000,
      timestamp: '2024-01-15T10:30:00',
      status: 'active',
      gttOrderId: null
    },
    {
      id: 2,
      symbol: 'TCS',
      companyName: 'Tata Consultancy Services',
      action: 'SELL',
      triggerPrice: 3245.20,
      currentPrice: 3250.80,
      weeklyHigh: 3280.50,
      confidence: 92,
      volume: 890000,
      timestamp: '2024-01-15T09:45:00',
      status: 'executed',
      gttOrderId: 'GTT_TCS_001'
    },
    {
      id: 3,
      symbol: 'INFY',
      companyName: 'Infosys Limited',
      action: 'BUY',
      triggerPrice: 1456.30,
      currentPrice: 1445.80,
      weeklyHigh: 1456.30,
      confidence: 78,
      volume: 2100000,
      timestamp: '2024-01-15T09:15:00',
      status: 'pending',
      gttOrderId: null
    }
  ]);

  const [gttOrders, setGttOrders] = useState([
    {
      id: 'GTT_REL_001',
      symbol: 'RELIANCE',
      orderType: 'BUY',
      triggerPrice: 2456.75,
      quantity: 10,
      status: 'active',
      createdAt: '2024-01-15T10:30:00',
      validTill: '2024-01-22T15:30:00'
    },
    {
      id: 'GTT_TCS_001',
      symbol: 'TCS',
      orderType: 'SELL',
      triggerPrice: 3245.20,
      quantity: 5,
      status: 'executed',
      createdAt: '2024-01-15T09:45:00',
      executedAt: '2024-01-15T11:20:00'
    }
  ]);

  const [holdings, setHoldings] = useState([
    {
      symbol: 'RELIANCE',
      quantity: 10,
      avgPrice: 2400.50,
      currentPrice: 2445.30,
      marketValue: 24453.00,
      pnl: 447.00,
      pnlPercent: 1.86,
      dayChange: 12.50,
      dayChangePercent: 0.51
    },
    {
      symbol: 'INFY',
      quantity: 15,
      avgPrice: 1420.00,
      currentPrice: 1445.80,
      marketValue: 21687.00,
      pnl: 387.00,
      pnlPercent: 1.82,
      dayChange: -8.20,
      dayChangePercent: -0.56
    }
  ]);

  const [strategyEnabled] = useState(true);

  useEffect(() => {
    // Subscribe to real-time data for signals
    if (isSmartAPIConnected && signals.length > 0) {
      const symbols = signals.map(signal => signal.symbol);
      subscribeToSymbols(symbols);
      
      return () => {
        unsubscribeFromSymbols(symbols);
      };
    }
  }, [isSmartAPIConnected, signals, subscribeToSymbols, unsubscribeFromSymbols]);

  const handlePlaceGTTOrder = async (signal) => {
    try {
      const orderData = {
        symbol: signal.symbol,
        action: signal.action,
        triggerPrice: signal.triggerPrice,
        quantity: calculateQuantity(signal.triggerPrice),
        orderType: 'GTT'
      };

      // API call to place GTT order
      const response = await fetch('/api/trading/place-gtt-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update signal status
        setSignals(prev => prev.map(s => 
          s.id === signal.id 
            ? { ...s, status: 'gtt_placed', gttOrderId: result.order_id }
            : s
        ));

        // Add to GTT orders
        setGttOrders(prev => [...prev, {
          id: result.order_id,
          symbol: signal.symbol,
          orderType: signal.action,
          triggerPrice: signal.triggerPrice,
          quantity: orderData.quantity,
          status: 'active',
          createdAt: new Date().toISOString(),
          validTill: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        }]);
      }
    } catch (error) {
      console.error('Error placing GTT order:', error);
    }
  };

  const calculateQuantity = (price) => {
    // Calculate quantity based on ₹2000 per trade limit
    return Math.floor(2000 / price);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#10b981';
      case 'executed': return '#3b82f6';
      case 'pending': return '#f59e0b';
      case 'gtt_placed': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="weekly-high-strategy">
      <PageHeader
        title="Weekly High Strategy"
        subtitle="Automated breakout detection with GTT order management"
        variant="primary"
      >
        <div className="strategy-controls">
          <div className="strategy-status">
            <span className={`status-indicator ${strategyEnabled ? 'active' : 'inactive'}`}>
              {strategyEnabled ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      </PageHeader>

      {/* Strategy Metrics */}
      <div className="strategy-metrics">
        <Card variant="elevated" hover className="metric-card">
          <Card.Content>
            <div className="metric-header">
              <div className="metric-icon">📊</div>
              <Card.Subtitle>Active Signals</Card.Subtitle>
            </div>
            <Card.Title className="metric-value">{signals.filter(s => s.status === 'active').length}</Card.Title>
          </Card.Content>
        </Card>

        <Card variant="elevated" hover className="metric-card">
          <Card.Content>
            <div className="metric-header">
              <div className="metric-icon">🎯</div>
              <Card.Subtitle>Success Rate</Card.Subtitle>
            </div>
            <Card.Title className="metric-value positive">85%</Card.Title>
          </Card.Content>
        </Card>

        <Card variant="elevated" hover className="metric-card">
          <Card.Content>
            <div className="metric-header">
              <div className="metric-icon">💰</div>
              <Card.Subtitle>Total P&L</Card.Subtitle>
            </div>
            <Card.Title className="metric-value positive">+₹15,420</Card.Title>
          </Card.Content>
        </Card>

        <Card variant="elevated" hover className="metric-card">
          <Card.Content>
            <div className="metric-header">
              <div className="metric-icon">📈</div>
              <Card.Subtitle>GTT Orders</Card.Subtitle>
            </div>
            <Card.Title className="metric-value">{gttOrders.filter(o => o.status === 'active').length}</Card.Title>
          </Card.Content>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button 
          className={`tab-btn ${activeTab === 'signals' ? 'active' : ''}`}
          onClick={() => setActiveTab('signals')}
        >
          Signals & Analysis
        </button>
        <button 
          className={`tab-btn ${activeTab === 'gtt' ? 'active' : ''}`}
          onClick={() => setActiveTab('gtt')}
        >
          GTT Orders
        </button>
        <button 
          className={`tab-btn ${activeTab === 'holdings' ? 'active' : ''}`}
          onClick={() => setActiveTab('holdings')}
        >
          Current Holdings
        </button>

      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'signals' && (
          <div className="signals-tab">
            <div className="signals-header">
              <h2>Recent Signals</h2>
              <div className="signals-filters">
                <select className="filter-select">
                  <option value="all">All Signals</option>
                  <option value="active">Active</option>
                  <option value="executed">Executed</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
            </div>
            
            <div className="signals-list">
              {signals.map(signal => (
                <div key={signal.id} className="signal-card">
                  <div className="signal-header">
                    <div className="signal-info">
                      <h3 className="signal-symbol">{signal.symbol}</h3>
                      <p className="signal-company">{signal.companyName}</p>
                    </div>
                    <div className="signal-status">
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: getStatusColor(signal.status) }}
                      >
                        {signal.status}
                      </span>
                    </div>
                  </div>
                  
                  <div className="signal-details">
                    <div className="signal-prices">
                      <div className="price-item">
                        <span className="price-label">Current Price</span>
                        <span className="price-value">₹{signal.currentPrice}</span>
                      </div>
                      <div className="price-item">
                        <span className="price-label">Trigger Price</span>
                        <span className="price-value">₹{signal.triggerPrice}</span>
                      </div>
                      <div className="price-item">
                        <span className="price-label">Weekly High</span>
                        <span className="price-value">₹{signal.weeklyHigh}</span>
                      </div>
                    </div>
                    
                    <div className="signal-meta">
                      <div className="meta-item">
                        <span className="meta-label">Action</span>
                        <span className={`action-badge ${signal.action.toLowerCase()}`}>
                          {signal.action}
                        </span>
                      </div>
                      <div className="meta-item">
                        <span className="meta-label">Confidence</span>
                        <span className="meta-value">{signal.confidence}%</span>
                      </div>
                      <div className="meta-item">
                        <span className="meta-label">Volume</span>
                        <span className="meta-value">{(signal.volume / 1000000).toFixed(1)}M</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="signal-actions">
                    {signal.status === 'active' && !signal.gttOrderId && (
                      <button 
                        className="gtt-btn"
                        onClick={() => handlePlaceGTTOrder(signal)}
                        disabled={!isSmartAPIConnected}
                      >
                        Place GTT Order
                      </button>
                    )}
                    {signal.gttOrderId && (
                      <span className="gtt-placed">GTT Order Placed</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'gtt' && (
          <div className="gtt-tab">
            <div className="gtt-header">
              <h2>GTT Orders</h2>
              <div className="gtt-summary">
                <span>Active: {gttOrders.filter(o => o.status === 'active').length}</span>
                <span>Executed: {gttOrders.filter(o => o.status === 'executed').length}</span>
              </div>
            </div>
            
            <div className="gtt-list">
              {gttOrders.map(order => (
                <div key={order.id} className="gtt-card">
                  <div className="gtt-header-info">
                    <div className="gtt-symbol">{order.symbol}</div>
                    <div className="gtt-status">
                      <span 
                        className="status-badge"
                        style={{ backgroundColor: getStatusColor(order.status) }}
                      >
                        {order.status}
                      </span>
                    </div>
                  </div>
                  
                  <div className="gtt-details">
                    <div className="gtt-item">
                      <span className="gtt-label">Order Type</span>
                      <span className={`gtt-value ${order.orderType.toLowerCase()}`}>
                        {order.orderType}
                      </span>
                    </div>
                    <div className="gtt-item">
                      <span className="gtt-label">Trigger Price</span>
                      <span className="gtt-value">₹{order.triggerPrice}</span>
                    </div>
                    <div className="gtt-item">
                      <span className="gtt-label">Quantity</span>
                      <span className="gtt-value">{order.quantity}</span>
                    </div>
                    <div className="gtt-item">
                      <span className="gtt-label">Created</span>
                      <span className="gtt-value">
                        {new Date(order.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  
                  {order.status === 'active' && (
                    <div className="gtt-actions">
                      <button className="cancel-gtt-btn">Cancel Order</button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'holdings' && (
          <div className="holdings-tab">
            <div className="holdings-header">
              <h2>Current Holdings</h2>
              <div className="holdings-summary">
                <span>Total Value: ₹{holdings.reduce((sum, h) => sum + h.marketValue, 0).toLocaleString()}</span>
                <span>Total P&L: ₹{holdings.reduce((sum, h) => sum + h.pnl, 0).toLocaleString()}</span>
              </div>
            </div>
            
            <div className="holdings-list">
              {holdings.map((holding, index) => (
                <div key={index} className="holding-card">
                  <div className="holding-header">
                    <h3 className="holding-symbol">{holding.symbol}</h3>
                    <div className="holding-quantity">{holding.quantity} shares</div>
                  </div>
                  
                  <div className="holding-details">
                    <div className="holding-prices">
                      <div className="price-row">
                        <span>Avg Price: ₹{holding.avgPrice}</span>
                        <span>Current: ₹{holding.currentPrice}</span>
                      </div>
                      <div className="price-row">
                        <span>Market Value: ₹{holding.marketValue.toLocaleString()}</span>
                        <span className={holding.pnl >= 0 ? 'positive' : 'negative'}>
                          P&L: ₹{holding.pnl} ({holding.pnlPercent.toFixed(2)}%)
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}


      </div>
    </div>
  );
};

export default WeeklyHighStrategy;
