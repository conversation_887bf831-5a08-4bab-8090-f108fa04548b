import React from 'react';
import './StrategyComingSoon.css';

const RSIStrategy = ({ userSubscription, hasAccess, onNavigate }) => {
  const hasRSIAccess = hasAccess('professional');

  if (!hasRSIAccess) {
    return (
      <div className="strategy-locked">
        <div className="locked-content">
          <div className="lock-icon">🔒</div>
          <h1>RSI Strategy</h1>
          <p>Advanced momentum indicators with precise entry and exit signals</p>
          
          <div className="feature-preview">
            <h3>What you'll get with Professional Plan:</h3>
            <ul className="feature-list">
              <li>✨ RSI-based signal generation</li>
              <li>📊 Overbought/Oversold detection</li>
              <li>🎯 Multi-timeframe analysis</li>
              <li>⚡ Real-time momentum alerts</li>
              <li>📈 Advanced backtesting</li>
              <li>🔄 Automated GTT orders</li>
            </ul>
          </div>

          <div className="upgrade-section">
            <h3>Upgrade to Professional</h3>
            <div className="pricing-highlight">
              <div className="price">₹5,999<span>/month</span></div>
              <div className="savings">Save 17% with yearly plan</div>
            </div>
            
            <button 
              className="upgrade-btn"
              onClick={() => onNavigate('upgrade-subscription')}
            >
              Upgrade Now
            </button>
            
            <div className="upgrade-benefits">
              <p>✓ All Starter features included</p>
              <p>✓ RSI Strategy access</p>
              <p>✓ Advanced backtesting</p>
              <p>✓ Priority support</p>
            </div>
          </div>
        </div>
        
        <div className="preview-dashboard">
          <div className="preview-header">
            <h4>RSI Strategy Dashboard Preview</h4>
            <span className="preview-badge">Professional Feature</span>
          </div>
          
          <div className="preview-content">
            <div className="preview-metrics">
              <div className="preview-metric">
                <span className="metric-label">RSI Signals</span>
                <span className="metric-value">12</span>
              </div>
              <div className="preview-metric">
                <span className="metric-label">Success Rate</span>
                <span className="metric-value">78%</span>
              </div>
              <div className="preview-metric">
                <span className="metric-label">Avg Return</span>
                <span className="metric-value">+4.2%</span>
              </div>
            </div>
            
            <div className="preview-signals">
              <div className="preview-signal">
                <div className="signal-info">
                  <span className="signal-symbol">HDFC</span>
                  <span className="signal-action buy">BUY</span>
                </div>
                <div className="signal-details">
                  <span>RSI: 25 (Oversold)</span>
                  <span>Confidence: 85%</span>
                </div>
              </div>
              
              <div className="preview-signal">
                <div className="signal-info">
                  <span className="signal-symbol">ICICI</span>
                  <span className="signal-action sell">SELL</span>
                </div>
                <div className="signal-details">
                  <span>RSI: 78 (Overbought)</span>
                  <span>Confidence: 92%</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="preview-overlay">
            <div className="overlay-content">
              <div className="overlay-icon">🔒</div>
              <p>Upgrade to Professional to unlock</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user has access, show the actual RSI strategy component
  return (
    <div className="rsi-strategy">
      <div className="page-header">
        <h1>RSI Strategy</h1>
        <p>Advanced momentum indicators with precise entry and exit signals</p>
      </div>
      
      <div className="coming-soon-notice">
        <div className="notice-content">
          <h2>🚧 Coming Soon</h2>
          <p>The RSI Strategy is currently under development and will be available soon.</p>
          <div className="eta">
            <strong>Expected Release:</strong> Q2 2024
          </div>
        </div>
      </div>
      
      {/* Placeholder for future RSI strategy implementation */}
      <div className="strategy-placeholder">
        <div className="placeholder-metrics">
          <div className="placeholder-card">
            <h3>RSI Signals</h3>
            <div className="placeholder-value">Coming Soon</div>
          </div>
          <div className="placeholder-card">
            <h3>Momentum Analysis</h3>
            <div className="placeholder-value">Coming Soon</div>
          </div>
          <div className="placeholder-card">
            <h3>Signal Accuracy</h3>
            <div className="placeholder-value">Coming Soon</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RSIStrategy;
