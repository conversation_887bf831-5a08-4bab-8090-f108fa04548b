import React, { forwardRef } from 'react';
import './Input.css';

const Input = forwardRef(({
  label,
  error,
  helperText,
  icon,
  iconPosition = 'left',
  size = 'md',
  variant = 'default',
  fullWidth = false,
  className = '',
  id,
  ...props
}, ref) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  
  const baseClasses = 'input-field';
  const sizeClasses = `input-field--${size}`;
  const variantClasses = `input-field--${variant}`;
  const stateClasses = [
    error && 'input-field--error',
    fullWidth && 'input-field--full-width',
    icon && `input-field--with-icon-${iconPosition}`
  ].filter(Boolean).join(' ');

  const inputClasses = [
    baseClasses,
    sizeClasses,
    variantClasses,
    stateClasses,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className="input-group">
      {label && (
        <label htmlFor={inputId} className="input-label">
          {label}
        </label>
      )}
      
      <div className="input-container">
        {icon && iconPosition === 'left' && (
          <div className="input-icon input-icon--left">
            {icon}
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          className={inputClasses}
          {...props}
        />
        
        {icon && iconPosition === 'right' && (
          <div className="input-icon input-icon--right">
            {icon}
          </div>
        )}
      </div>
      
      {(error || helperText) && (
        <div className="input-feedback">
          {error && (
            <span className="input-error">
              {error}
            </span>
          )}
          {!error && helperText && (
            <span className="input-helper">
              {helperText}
            </span>
          )}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
