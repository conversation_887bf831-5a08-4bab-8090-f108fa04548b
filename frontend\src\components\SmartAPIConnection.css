.smartapi-connection {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.connection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-indicator {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 1rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  animation: pulse 2s infinite;
}

.status-indicator.connected .status-dot {
  background-color: #27ae60;
}

.status-indicator.disconnected .status-dot {
  background-color: #e74c3c;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.connect-btn, .disconnect-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.connect-btn {
  background: #3498db;
  color: white;
}

.connect-btn:hover:not(:disabled) {
  background: #2980b9;
}

.connect-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.disconnect-btn {
  background: #e74c3c;
  color: white;
}

.disconnect-btn:hover {
  background: #c0392b;
}

.user-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #27ae60;
}

.user-info p {
  margin: 5px 0;
  color: #2c3e50;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  border: 1px solid #f5c6cb;
}

.credentials-form {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 15px;
  border: 1px solid #e9ecef;
}

.credentials-form h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  text-align: center;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #2c3e50;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 2px solid #e9ecef;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
}

.form-group small {
  display: block;
  margin-top: 5px;
  color: #6c757d;
  font-size: 0.8rem;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #5a6268;
}

.submit-btn {
  background: #27ae60;
  color: white;
}

.submit-btn:hover:not(:disabled) {
  background: #229954;
}

.submit-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.security-note {
  margin-top: 20px;
  padding: 15px;
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 5px;
}

.security-note p {
  margin: 0;
  color: #0c5460;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .connection-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .status-indicator {
    justify-content: center;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .cancel-btn, .submit-btn {
    width: 100%;
  }
}

/* Animation for form appearance */
.credentials-form {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
