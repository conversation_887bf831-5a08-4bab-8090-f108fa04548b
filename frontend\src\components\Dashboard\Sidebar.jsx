import React, { useState } from 'react';
import './Sidebar.css';

const Sidebar = ({ 
  navigationItems, 
  activeView, 
  onNavigate, 
  collapsed, 
  onToggleCollapse,
  userSubscription,
  hasAccess 
}) => {
  const [expandedMenus, setExpandedMenus] = useState(['strategies']);

  const toggleSubmenu = (menuId) => {
    setExpandedMenus(prev => 
      prev.includes(menuId) 
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  const getSubscriptionBadge = (subscription) => {
    const badges = {
      'starter': { label: 'Starter', color: '#10b981' },
      'professional': { label: 'Pro', color: '#3b82f6' },
      'enterprise': { label: 'Enterprise', color: '#8b5cf6' }
    };
    return badges[subscription] || badges.starter;
  };

  const renderNavigationItem = (item) => {
    const isActive = activeView === item.id;
    const isExpanded = expandedMenus.includes(item.id);

    if (item.submenu) {
      return (
        <div key={item.id} className="nav-group">
          <div 
            className={`nav-item nav-group-header ${isExpanded ? 'expanded' : ''}`}
            onClick={() => toggleSubmenu(item.id)}
          >
            <div className="nav-item-content">
              <span className="nav-icon">{item.icon}</span>
              {!collapsed && (
                <>
                  <span className="nav-label">{item.label}</span>
                  <span className={`nav-arrow ${isExpanded ? 'rotated' : ''}`}>
                    ▼
                  </span>
                </>
              )}
            </div>
          </div>
          
          {!collapsed && isExpanded && (
            <div className="nav-submenu">
              {item.submenu.map(subItem => renderSubmenuItem(subItem))}
            </div>
          )}
        </div>
      );
    }

    return (
      <div 
        key={item.id}
        className={`nav-item ${isActive ? 'active' : ''} ${!item.enabled ? 'disabled' : ''}`}
        onClick={() => item.enabled && onNavigate(item.id)}
      >
        <div className="nav-item-content">
          <span className="nav-icon">{item.icon}</span>
          {!collapsed && <span className="nav-label">{item.label}</span>}
        </div>
      </div>
    );
  };

  const renderSubmenuItem = (subItem) => {
    const isActive = activeView === subItem.id;
    const hasSubscriptionAccess = hasAccess(subItem.subscription);
    const badge = getSubscriptionBadge(subItem.subscription);

    return (
      <div 
        key={subItem.id}
        className={`nav-subitem ${isActive ? 'active' : ''} ${!subItem.enabled ? 'disabled' : ''}`}
        onClick={() => {
          if (subItem.enabled && hasSubscriptionAccess) {
            onNavigate(subItem.id);
          } else if (!hasSubscriptionAccess) {
            // Show upgrade modal or redirect to pricing
            onNavigate('upgrade-subscription');
          }
        }}
      >
        <div className="nav-subitem-content">
          <span className="nav-subicon">{subItem.icon}</span>
          <div className="nav-subitem-info">
            <span className="nav-sublabel">{subItem.label}</span>
            {subItem.comingSoon && (
              <span className="coming-soon-badge">Coming Soon</span>
            )}
            {!hasSubscriptionAccess && (
              <span 
                className="subscription-badge"
                style={{ backgroundColor: badge.color }}
              >
                {badge.label}
              </span>
            )}
          </div>
          {!subItem.enabled && !hasSubscriptionAccess && (
            <span className="lock-icon">🔒</span>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`sidebar ${collapsed ? 'collapsed' : ''}`}>
      <div className="sidebar-header">
        <div className="sidebar-brand">
          {!collapsed && (
            <>
              <h2>Niveshtor AI</h2>
              <span className="brand-subtitle">Trading Platform</span>
            </>
          )}
          {collapsed && <span className="brand-icon">📊</span>}
        </div>
        <button 
          className="sidebar-toggle"
          onClick={onToggleCollapse}
          title={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {collapsed ? '▶' : '◀'}
        </button>
      </div>

      {!collapsed && (
        <div className="subscription-info">
          <div className="subscription-card">
            <div className="subscription-header">
              <span 
                className="subscription-badge current"
                style={{ backgroundColor: getSubscriptionBadge(userSubscription).color }}
              >
                {getSubscriptionBadge(userSubscription).label}
              </span>
              <span className="subscription-status">Active</span>
            </div>
            <p className="subscription-description">
              {userSubscription === 'starter' && 'Basic trading strategies'}
              {userSubscription === 'professional' && 'Advanced strategies & tools'}
              {userSubscription === 'enterprise' && 'All features & custom strategies'}
            </p>
            {userSubscription !== 'enterprise' && (
              <button 
                className="upgrade-button"
                onClick={() => onNavigate('upgrade-subscription')}
              >
                Upgrade Plan
              </button>
            )}
          </div>
        </div>
      )}

      <nav className="sidebar-nav">
        {navigationItems.map(item => renderNavigationItem(item))}
      </nav>

      {!collapsed && (
        <div className="sidebar-footer">
          <div className="quick-actions">
            <button 
              className="quick-action-btn"
              onClick={() => onNavigate('smartapi-connect')}
              title="Connect Angel One"
            >
              <span className="action-icon">🔗</span>
              <span className="action-label">Connect Broker</span>
            </button>
            <button 
              className="quick-action-btn"
              onClick={() => onNavigate('help')}
              title="Help & Support"
            >
              <span className="action-icon">❓</span>
              <span className="action-label">Help</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
