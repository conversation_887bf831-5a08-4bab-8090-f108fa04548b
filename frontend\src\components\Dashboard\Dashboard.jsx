import React, { useState, useEffect } from 'react';
import Sidebar from './Sidebar';
import Header from './Header';
import Overview from './pages/Overview';
import WeeklyHighStrategy from './pages/strategies/WeeklyHighStrategy';
import RSIStrategy from './pages/strategies/RSIStrategy';
import ConsolidatedBreakoutStrategy from './pages/strategies/ConsolidatedBreakoutStrategy';
import UniversalTools from './pages/UniversalTools';
import CapitalManagement from './pages/universal/CapitalManagement';
import Backtesting from './pages/Backtesting';
import Settings from './pages/Settings';
import SystemStatus from './pages/SystemStatus';
import SmartAPIConnection from '../SmartAPIConnection';
import useWebSocket from '../../hooks/useWebSocket';
import '../../styles/design-system.css';
import './Dashboard.css';

const Dashboard = ({ user, onLogout }) => {
  const [activeView, setActiveView] = useState('overview');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isSmartAPIConnected, setIsSmartAPIConnected] = useState(false);
  const [userSubscription, setUserSubscription] = useState('starter'); // starter, professional, enterprise

  // WebSocket for real-time data
  const {
    connectionStatus,
    marketData,
    orderUpdates,
    subscribeToSymbols,
    unsubscribeFromSymbols,
    isConnected: wsConnected
  } = useWebSocket(user?.id, isSmartAPIConnected);

  // Navigation structure
  const navigationItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: '📊',
      component: Overview,
      enabled: true
    },
    {
      id: 'strategies',
      label: 'Strategies',
      icon: '🎯',
      enabled: true,
      submenu: [
        {
          id: 'weekly-high',
          label: 'Weekly High Strategy',
          icon: '📈',
          component: WeeklyHighStrategy,
          enabled: true,
          subscription: 'starter'
        },
        {
          id: 'rsi-strategy',
          label: 'RSI Strategy',
          icon: '⚡',
          component: RSIStrategy,
          enabled: userSubscription !== 'starter',
          subscription: 'professional',
          comingSoon: userSubscription === 'starter'
        },
        {
          id: 'consolidated-breakout',
          label: 'Consolidated Breakout',
          icon: '🎯',
          component: ConsolidatedBreakoutStrategy,
          enabled: userSubscription === 'enterprise',
          subscription: 'enterprise',
          comingSoon: userSubscription !== 'enterprise'
        }
      ]
    },
    {
      id: 'tools',
      label: 'Universal Tools',
      icon: '🛠️',
      enabled: true,
      submenu: [
        {
          id: 'capital-management',
          label: 'Capital Management',
          icon: '💰',
          component: CapitalManagement,
          enabled: true,
          subscription: 'starter'
        }
      ]
    },
    {
      id: 'backtesting',
      label: 'Backtesting',
      icon: '📈',
      component: Backtesting,
      enabled: true
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: '⚙️',
      component: Settings,
      enabled: true
    },
    {
      id: 'system-status',
      label: 'System Status',
      icon: '🔧',
      component: SystemStatus,
      enabled: true
    }
  ];

  // Get current component to render
  const getCurrentComponent = () => {
    // Check if it's a strategy submenu item
    for (const item of navigationItems) {
      if (item.submenu) {
        const submenuItem = item.submenu.find(sub => sub.id === activeView);
        if (submenuItem) {
          return submenuItem.component;
        }
      }
    }
    
    // Check main navigation items
    const mainItem = navigationItems.find(item => item.id === activeView);
    return mainItem?.component || Overview;
  };

  const CurrentComponent = getCurrentComponent();

  // Handle navigation
  const handleNavigation = (viewId) => {
    setActiveView(viewId);
  };

  // Handle SmartAPI connection change
  const handleSmartAPIConnection = (connected) => {
    setIsSmartAPIConnected(connected);
  };

  // Check user subscription and permissions
  const hasAccess = (requiredSubscription) => {
    const subscriptionLevels = {
      'starter': 1,
      'professional': 2,
      'enterprise': 3
    };
    
    return subscriptionLevels[userSubscription] >= subscriptionLevels[requiredSubscription];
  };

  return (
    <div className="dashboard">
      <Sidebar
        navigationItems={navigationItems}
        activeView={activeView}
        onNavigate={handleNavigation}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        userSubscription={userSubscription}
        hasAccess={hasAccess}
      />
      
      <div className={`dashboard-main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <Header
          user={user}
          onLogout={onLogout}
          isSmartAPIConnected={isSmartAPIConnected}
          connectionStatus={connectionStatus}
          userSubscription={userSubscription}
        />
        
        <div className="dashboard-content">

          {/* SmartAPI Connection Component */}
          {activeView === 'smartapi-connect' && (
            <div className="page-container">
              <div className="page-header">
                <h1>Connect to Angel One</h1>
                <p>Securely connect your Angel One account for live trading</p>
              </div>
              <SmartAPIConnection onConnectionChange={handleSmartAPIConnection} />
            </div>
          )}

          {/* Main Content */}
          {activeView !== 'smartapi-connect' && (
            <div className="page-container">
              <CurrentComponent
                user={user}
                isSmartAPIConnected={isSmartAPIConnected}
                marketData={marketData}
                orderUpdates={orderUpdates}
                subscribeToSymbols={subscribeToSymbols}
                unsubscribeFromSymbols={unsubscribeFromSymbols}
                wsConnected={wsConnected}
                userSubscription={userSubscription}
                hasAccess={hasAccess}
                onNavigate={handleNavigation}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
