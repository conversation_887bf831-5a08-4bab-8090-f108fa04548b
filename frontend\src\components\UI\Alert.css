/* <PERSON>ert Component */
.alert {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  border-radius: var(--radius-lg);
  border: 1px solid;
  font-family: var(--font-family-sans);
  transition: all var(--transition-base);
}

/* <PERSON><PERSON> Sizes */
.alert--sm {
  padding: var(--space-3);
  font-size: var(--text-sm);
}

.alert--md {
  padding: var(--space-4);
  font-size: var(--text-base);
}

.alert--lg {
  padding: var(--space-5);
  font-size: var(--text-lg);
}

/* <PERSON><PERSON> Variants */
.alert--info {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-800);
}

.alert--success {
  background: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-800);
}

.alert--warning {
  background: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.alert--error {
  background: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}

/* Alert Content */
.alert__content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  flex: 1;
}

.alert__icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.alert--info .alert__icon {
  color: var(--primary-600);
}

.alert--success .alert__icon {
  color: var(--success-600);
}

.alert--warning .alert__icon {
  color: var(--warning-600);
}

.alert--error .alert__icon {
  color: var(--error-600);
}

.alert__body {
  flex: 1;
  min-width: 0;
}

.alert__title {
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
  line-height: var(--leading-tight);
}

.alert__message {
  line-height: var(--leading-relaxed);
}

/* Dismiss Button */
.alert__dismiss {
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
  margin-left: var(--space-2);
  margin-top: -2px;
}

.alert--info .alert__dismiss {
  color: var(--primary-500);
}

.alert--success .alert__dismiss {
  color: var(--success-500);
}

.alert--warning .alert__dismiss {
  color: var(--warning-500);
}

.alert--error .alert__dismiss {
  color: var(--error-500);
}

.alert__dismiss:hover {
  opacity: 0.8;
  transform: scale(1.1);
}

.alert__dismiss:focus {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* Dismissible Alert */
.alert--dismissible {
  padding-right: var(--space-3);
}

/* Solid Variants */
.alert--info-solid {
  background: var(--primary-600);
  border-color: var(--primary-600);
  color: white;
}

.alert--success-solid {
  background: var(--success-600);
  border-color: var(--success-600);
  color: white;
}

.alert--warning-solid {
  background: var(--warning-600);
  border-color: var(--warning-600);
  color: white;
}

.alert--error-solid {
  background: var(--error-600);
  border-color: var(--error-600);
  color: white;
}

.alert--info-solid .alert__icon,
.alert--success-solid .alert__icon,
.alert--warning-solid .alert__icon,
.alert--error-solid .alert__icon,
.alert--info-solid .alert__dismiss,
.alert--success-solid .alert__dismiss,
.alert--warning-solid .alert__dismiss,
.alert--error-solid .alert__dismiss {
  color: white;
}

/* Outline Variants */
.alert--info-outline {
  background: transparent;
  border-color: var(--primary-300);
  color: var(--primary-700);
}

.alert--success-outline {
  background: transparent;
  border-color: var(--success-300);
  color: var(--success-700);
}

.alert--warning-outline {
  background: transparent;
  border-color: var(--warning-300);
  color: var(--warning-700);
}

.alert--error-outline {
  background: transparent;
  border-color: var(--error-300);
  color: var(--error-700);
}

/* Animation */
.alert {
  animation: alert-slide-in 0.3s ease-out;
}

@keyframes alert-slide-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .alert--sm {
    padding: var(--space-2);
    font-size: var(--text-xs);
  }
  
  .alert--md {
    padding: var(--space-3);
    font-size: var(--text-sm);
  }
  
  .alert--lg {
    padding: var(--space-4);
    font-size: var(--text-base);
  }
  
  .alert__content {
    gap: var(--space-2);
  }
  
  .alert__dismiss {
    margin-left: var(--space-1);
  }
}
