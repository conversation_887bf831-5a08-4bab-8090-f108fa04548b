# Niveshtor AI - Trading Web App

## 🚀 Modern Tech Stack

### Frontend
- **React with Vite** - Fast development server, stable hot reload
- **Modern UI Components** - Responsive trading interface

### Backend
- **Python FastAPI** - High-performance API for financial data
- **PostgreSQL** - Reliable database for trading data
- **SmartAPI Integration** - Angel One trading API
- **Yahoo Finance** - Market data via yfinance

### Key Features
- Real-time market data
- Trading execution via SmartAPI
- Portfolio management
- Technical analysis tools

## Project Structure

```
niveshtor-ai/
├── frontend/          # React + Vite application
├── backend/           # FastAPI application
├── database/          # Database schemas and migrations
├── smartapi-python-main/  # SmartAPI Python SDK
└── docs/             # Documentation
```

## Getting Started

### Prerequisites
- Node.js 18+ 
- Python 3.8+
- PostgreSQL 12+

### Installation
1. Install frontend dependencies: `cd frontend && npm install`
2. Install backend dependencies: `cd backend && pip install -r requirements.txt`
3. Setup database: `createdb niveshtor_ai`
4. Run development servers

## Development

- Frontend: `cd frontend && npm run dev`
- Backend: `cd backend && uvicorn main:app --reload`

## API Documentation
- FastAPI docs: http://localhost:8000/docs
- Frontend: http://localhost:5173
