import React, { useState } from 'react';
import { validateEmail } from '../../utils/validation';

const ForgotPasswordForm = ({ onSubmit, isLoading, onSwitchToLogin }) => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');

  const handleChange = (e) => {
    setEmail(e.target.value);
    if (error) {
      setError('');
    }
  };

  const validateForm = () => {
    if (!email.trim()) {
      setError('Email address is required');
      return false;
    }
    
    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    onSubmit(email);
  };

  return (
    <form onSubmit={handleSubmit} className="auth-form">
      <div className="forgot-password-info">
        <div className="info-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        </div>
        <h3>Forgot your password?</h3>
        <p>
          No worries! Enter your email address below and we'll send you a link to reset your password.
        </p>
      </div>

      <div className="form-group">
        <label htmlFor="email" className="form-label">
          Email Address
        </label>
        <div className="input-container">
          <input
            type="email"
            id="email"
            name="email"
            value={email}
            onChange={handleChange}
            className={`form-input ${error ? 'error' : ''}`}
            placeholder="Enter your email address"
            disabled={isLoading}
            autoComplete="email"
            autoFocus
          />
          <div className="input-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
          </div>
        </div>
        {error && (
          <span className="form-error">{error}</span>
        )}
      </div>

      <button
        type="submit"
        className="auth-submit-btn"
        disabled={isLoading || !email.trim()}
      >
        {isLoading ? (
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <span>Sending reset link...</span>
          </div>
        ) : (
          'Send Reset Link'
        )}
      </button>

      <div className="forgot-password-help">
        <div className="help-section">
          <h4>Still having trouble?</h4>
          <ul>
            <li>Check your spam/junk folder for the reset email</li>
            <li>Make sure you're using the email associated with your account</li>
            <li>Contact support if you continue to have issues</li>
          </ul>
        </div>
        
        <div className="support-contact">
          <p>Need help? Contact our support team:</p>
          <a href="mailto:<EMAIL>" className="support-link">
            <EMAIL>
          </a>
        </div>
      </div>

      <div className="auth-switch">
        <span>Remember your password? </span>
        <button
          type="button"
          className="switch-link"
          onClick={onSwitchToLogin}
          disabled={isLoading}
        >
          Back to Sign In
        </button>
      </div>
    </form>
  );
};

export default ForgotPasswordForm;
