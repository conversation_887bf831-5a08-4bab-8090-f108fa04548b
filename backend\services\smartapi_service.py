from SmartApi import SmartConnect
from SmartApi.smartWebSocketV2 import SmartWebSocketV2
import logging
from typing import Optional, Dict, Any
import asyncio
import json
from datetime import datetime, timedelta
import hashlib
import base64

logger = logging.getLogger(__name__)

class SmartAPIService:
    def __init__(self, user_id: str = None):
        self.user_id = user_id
        self.api_key = None
        self.client_id = None
        self.password = None
        self.mpin = None
        self.totp = None
        self.smart_api = None
        self.auth_token = None
        self.feed_token = None
        self.refresh_token = None
        self.websocket = None
        self.session_expiry = None
        self.is_connected = False

    def set_credentials(self, api_key: str, client_id: str, password: str, mpin: str, totp: str):
        """Set user credentials for SmartAPI connection"""
        self.api_key = api_key
        self.client_id = client_id
        self.password = password
        self.mpin = mpin
        self.totp = totp

    async def login(self) -> Dict[str, Any]:
        """Login to SmartAPI with user credentials"""
        try:
            if not all([self.api_key, self.client_id, self.password]):
                return {
                    "success": False,
                    "error": "SmartAPI credentials not provided"
                }

            self.smart_api = SmartConnect(api_key=self.api_key)

            # Generate session
            data = self.smart_api.generateSession(
                clientCode=self.client_id,
                password=self.password,
                totp=self.totp
            )

            if data['status']:
                self.auth_token = data['data']['jwtToken']
                self.feed_token = data['data']['feedToken']
                self.refresh_token = data['data'].get('refreshToken')

                # Set session expiry (typically 24 hours)
                self.session_expiry = datetime.now() + timedelta(hours=24)
                self.is_connected = True

                logger.info(f"SmartAPI login successful for user {self.user_id}")

                return {
                    "success": True,
                    "message": "Connected to Angel One successfully",
                    "user_name": data['data'].get('uname', 'User'),
                    "session_expiry": self.session_expiry.isoformat()
                }
            else:
                logger.error(f"SmartAPI login failed: {data['message']}")
                return {
                    "success": False,
                    "error": data['message']
                }

        except Exception as e:
            logger.error(f"SmartAPI login error: {e}")
            return {
                "success": False,
                "error": f"Connection failed: {str(e)}"
            }
    
    def check_session(self) -> bool:
        """Check if session is still valid"""
        if not self.is_connected or not self.session_expiry:
            return False
        return datetime.now() < self.session_expiry

    async def refresh_session(self) -> Dict[str, Any]:
        """Refresh the session token"""
        try:
            if not self.refresh_token:
                return await self.login()

            data = self.smart_api.generateToken(self.refresh_token)

            if data['status']:
                self.auth_token = data['data']['jwtToken']
                self.feed_token = data['data']['feedToken']
                self.session_expiry = datetime.now() + timedelta(hours=24)

                return {
                    "success": True,
                    "message": "Session refreshed successfully"
                }
            else:
                return await self.login()  # Fallback to full login

        except Exception as e:
            logger.error(f"Session refresh error: {e}")
            return await self.login()  # Fallback to full login

    async def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Place an order through SmartAPI"""
        try:
            if not self.check_session():
                login_result = await self.refresh_session()
                if not login_result["success"]:
                    return login_result

            # Get symbol token
            symbol_token = await self.get_symbol_token(order_data["symbol"])
            if not symbol_token:
                return {
                    "success": False,
                    "error": f"Symbol {order_data['symbol']} not found"
                }

            # Map order data to SmartAPI format
            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": order_data["symbol"],
                "symboltoken": symbol_token,
                "transactiontype": order_data["order_type"],  # BUY or SELL
                "exchange": order_data.get("exchange", "NSE"),
                "ordertype": order_data["price_type"],  # MARKET or LIMIT
                "producttype": order_data.get("product_type", "INTRADAY"),
                "duration": "DAY",
                "price": str(order_data.get("price", "0")),
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(order_data["quantity"])
            }

            response = self.smart_api.placeOrder(order_params)

            if response['status']:
                return {
                    "success": True,
                    "order_id": response['data']['orderid'],
                    "message": "Order placed successfully"
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }

        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_orders(self) -> Dict[str, Any]:
        """Get order history"""
        try:
            if not self.check_session():
                login_result = await self.refresh_session()
                if not login_result["success"]:
                    return login_result

            response = self.smart_api.orderBook()

            if response['status']:
                # Process and format orders
                orders = []
                for order in response['data']:
                    orders.append({
                        "order_id": order.get('orderid'),
                        "symbol": order.get('tradingsymbol'),
                        "exchange": order.get('exchange'),
                        "order_type": order.get('transactiontype'),
                        "product_type": order.get('producttype'),
                        "quantity": int(order.get('quantity', 0)),
                        "price": float(order.get('price', 0)),
                        "status": order.get('orderstatus'),
                        "order_time": order.get('ordertime'),
                        "filled_quantity": int(order.get('filledshares', 0)),
                        "average_price": float(order.get('averageprice', 0))
                    })

                return {
                    "success": True,
                    "orders": orders
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }

        except Exception as e:
            logger.error(f"Error fetching orders: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_positions(self) -> Dict[str, Any]:
        """Get current positions"""
        try:
            if not self.check_session():
                login_result = await self.refresh_session()
                if not login_result["success"]:
                    return login_result

            response = self.smart_api.position()

            if response['status']:
                # Process and format positions
                positions = []
                for pos in response['data']:
                    positions.append({
                        "symbol": pos.get('tradingsymbol'),
                        "exchange": pos.get('exchange'),
                        "product_type": pos.get('producttype'),
                        "quantity": int(pos.get('netqty', 0)),
                        "average_price": float(pos.get('netvalue', 0)) / max(int(pos.get('netqty', 1)), 1),
                        "ltp": float(pos.get('ltp', 0)),
                        "pnl": float(pos.get('pnl', 0)),
                        "day_change": float(pos.get('daychange', 0)),
                        "day_change_percent": float(pos.get('daychangepercentage', 0))
                    })

                return {
                    "success": True,
                    "positions": positions
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }

        except Exception as e:
            logger.error(f"Error fetching positions: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_holdings(self) -> Dict[str, Any]:
        """Get portfolio holdings"""
        try:
            if not self.check_session():
                login_result = await self.refresh_session()
                if not login_result["success"]:
                    return login_result

            response = self.smart_api.holding()

            if response['status']:
                # Process and format holdings
                holdings = []
                total_invested = 0
                total_current = 0

                for holding in response['data']:
                    quantity = int(holding.get('quantity', 0))
                    avg_price = float(holding.get('averageprice', 0))
                    ltp = float(holding.get('ltp', 0))

                    invested_value = quantity * avg_price
                    current_value = quantity * ltp
                    pnl = current_value - invested_value
                    pnl_percent = (pnl / invested_value * 100) if invested_value > 0 else 0

                    total_invested += invested_value
                    total_current += current_value

                    holdings.append({
                        "symbol": holding.get('tradingsymbol'),
                        "exchange": holding.get('exchange'),
                        "quantity": quantity,
                        "average_price": avg_price,
                        "ltp": ltp,
                        "invested_value": invested_value,
                        "current_value": current_value,
                        "pnl": pnl,
                        "pnl_percent": pnl_percent,
                        "day_change": float(holding.get('daychange', 0)),
                        "day_change_percent": float(holding.get('daychangepercentage', 0))
                    })

                return {
                    "success": True,
                    "holdings": holdings,
                    "summary": {
                        "total_invested": total_invested,
                        "total_current": total_current,
                        "total_pnl": total_current - total_invested,
                        "total_pnl_percent": ((total_current - total_invested) / total_invested * 100) if total_invested > 0 else 0
                    }
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }

        except Exception as e:
            logger.error(f"Error fetching holdings: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_symbol_token(self, symbol: str) -> Optional[str]:
        """Get symbol token for trading"""
        try:
            # Search for symbol in instrument list
            response = self.smart_api.searchScrip("NSE", symbol)

            if response['status'] and response['data']:
                return response['data'][0]['symboltoken']
            else:
                # Try BSE if NSE fails
                response = self.smart_api.searchScrip("BSE", symbol)
                if response['status'] and response['data']:
                    return response['data'][0]['symboltoken']

            return None

        except Exception as e:
            logger.error(f"Error getting symbol token for {symbol}: {e}")
            return None

    async def get_ltp(self, symbol: str, exchange: str = "NSE") -> Dict[str, Any]:
        """Get Last Traded Price for a symbol"""
        try:
            if not self.check_session():
                login_result = await self.refresh_session()
                if not login_result["success"]:
                    return login_result

            symbol_token = await self.get_symbol_token(symbol)
            if not symbol_token:
                return {
                    "success": False,
                    "error": f"Symbol {symbol} not found"
                }

            response = self.smart_api.ltpData(exchange, symbol, symbol_token)

            if response['status']:
                data = response['data']
                return {
                    "success": True,
                    "symbol": symbol,
                    "ltp": float(data.get('ltp', 0)),
                    "open": float(data.get('open', 0)),
                    "high": float(data.get('high', 0)),
                    "low": float(data.get('low', 0)),
                    "close": float(data.get('close', 0)),
                    "volume": int(data.get('volume', 0))
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }

        except Exception as e:
            logger.error(f"Error getting LTP for {symbol}: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an existing order"""
        try:
            if not self.check_session():
                login_result = await self.refresh_session()
                if not login_result["success"]:
                    return login_result

            response = self.smart_api.cancelOrder(
                variety="NORMAL",
                orderid=order_id
            )

            if response['status']:
                return {
                    "success": True,
                    "message": "Order cancelled successfully"
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }

        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_profile(self) -> Dict[str, Any]:
        """Get user profile information"""
        try:
            if not self.check_session():
                login_result = await self.refresh_session()
                if not login_result["success"]:
                    return login_result

            response = self.smart_api.getProfile()

            if response['status']:
                profile = response['data']
                return {
                    "success": True,
                    "profile": {
                        "client_id": profile.get('clientcode'),
                        "name": profile.get('name'),
                        "email": profile.get('email'),
                        "mobile": profile.get('mobileno'),
                        "broker": profile.get('broker'),
                        "exchanges": profile.get('exchanges', [])
                    }
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }

        except Exception as e:
            logger.error(f"Error getting profile: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def logout(self) -> Dict[str, Any]:
        """Logout from SmartAPI"""
        try:
            if self.smart_api:
                response = self.smart_api.terminateSession(self.client_id)

            # Clear session data
            self.auth_token = None
            self.feed_token = None
            self.refresh_token = None
            self.session_expiry = None
            self.is_connected = False

            return {
                "success": True,
                "message": "Logged out successfully"
            }

        except Exception as e:
            logger.error(f"Error during logout: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# Service manager for multiple users
class SmartAPIManager:
    def __init__(self):
        self.user_services = {}

    def get_service(self, user_id: str) -> SmartAPIService:
        """Get or create SmartAPI service for a user"""
        if user_id not in self.user_services:
            self.user_services[user_id] = SmartAPIService(user_id)
        return self.user_services[user_id]

    def remove_service(self, user_id: str):
        """Remove SmartAPI service for a user"""
        if user_id in self.user_services:
            del self.user_services[user_id]

# Global manager instance
smartapi_manager = SmartAPIManager()
