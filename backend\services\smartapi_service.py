from SmartApi import SmartConnect
from SmartApi.smartWebSocketV2 import SmartWebSocketV2
import logging
from typing import Optional, Dict, Any
from config import settings
import asyncio

logger = logging.getLogger(__name__)

class SmartAPIService:
    def __init__(self):
        self.api_key = settings.SMARTAPI_API_KEY
        self.client_id = settings.SMARTAPI_CLIENT_ID
        self.password = settings.SMARTAPI_PASSWORD
        self.totp = settings.SMARTAPI_TOTP
        self.smart_api = None
        self.auth_token = None
        self.feed_token = None
        self.websocket = None
        
    async def login(self) -> bool:
        """Login to SmartAPI"""
        try:
            if not all([self.api_key, self.client_id, self.password]):
                logger.error("SmartAPI credentials not configured")
                return False
                
            self.smart_api = SmartConnect(api_key=self.api_key)
            
            # Generate session
            data = self.smart_api.generateSession(
                clientCode=self.client_id,
                password=self.password,
                totp=self.totp
            )
            
            if data['status']:
                self.auth_token = data['data']['jwtToken']
                self.feed_token = data['data']['feedToken']
                logger.info("SmartAPI login successful")
                return True
            else:
                logger.error(f"SmartAPI login failed: {data['message']}")
                return False
                
        except Exception as e:
            logger.error(f"SmartAPI login error: {e}")
            return False
    
    async def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Place an order through SmartAPI"""
        try:
            if not self.smart_api or not self.auth_token:
                await self.login()
            
            # Map order data to SmartAPI format
            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": order_data["symbol"],
                "symboltoken": await self.get_symbol_token(order_data["symbol"]),
                "transactiontype": order_data["order_type"],  # BUY or SELL
                "exchange": "NSE",
                "ordertype": order_data["price_type"],  # MARKET or LIMIT
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": str(order_data.get("price", "0")),
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(order_data["quantity"])
            }
            
            response = self.smart_api.placeOrder(order_params)
            
            if response['status']:
                return {
                    "success": True,
                    "order_id": response['data']['orderid'],
                    "message": "Order placed successfully"
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }
                
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_orders(self) -> Dict[str, Any]:
        """Get order history"""
        try:
            if not self.smart_api or not self.auth_token:
                await self.login()
            
            response = self.smart_api.orderBook()
            
            if response['status']:
                return {
                    "success": True,
                    "orders": response['data']
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }
                
        except Exception as e:
            logger.error(f"Error fetching orders: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_positions(self) -> Dict[str, Any]:
        """Get current positions"""
        try:
            if not self.smart_api or not self.auth_token:
                await self.login()
            
            response = self.smart_api.position()
            
            if response['status']:
                return {
                    "success": True,
                    "positions": response['data']
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }
                
        except Exception as e:
            logger.error(f"Error fetching positions: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_symbol_token(self, symbol: str) -> str:
        """Get symbol token for trading"""
        # This would typically involve looking up the symbol in a master file
        # For now, returning a placeholder
        return "1234"  # Replace with actual symbol lookup logic
    
    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an existing order"""
        try:
            if not self.smart_api or not self.auth_token:
                await self.login()
            
            response = self.smart_api.cancelOrder(
                variety="NORMAL",
                orderid=order_id
            )
            
            if response['status']:
                return {
                    "success": True,
                    "message": "Order cancelled successfully"
                }
            else:
                return {
                    "success": False,
                    "error": response['message']
                }
                
        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# Global instance
smartapi_service = SmartAPIService()
