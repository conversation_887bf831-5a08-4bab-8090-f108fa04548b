# 🚀 SmartAPI Integration Setup Guide

## Complete Angel One Trading Integration

Your Niveshtor AI trading platform now supports **real-time trading** with Angel One through SmartAPI integration where users can enter their credentials directly in the web app.

## ✅ What's Been Implemented

### 🔐 **Secure Credential Management**
- **Encrypted Storage**: User credentials are encrypted and stored securely in the database
- **Session Management**: Automatic token refresh and session handling
- **User-Friendly UI**: Clean interface for entering Angel One credentials

### 📊 **Real Trading Functions**
- **Order Placement**: Place BUY/SELL orders with MARKET/LIMIT types
- **Portfolio Fetching**: Get real holdings and positions from Angel One
- **Order Management**: View, track, and cancel orders
- **Position Tracking**: Real-time P&L and position updates

### 🔄 **Real-time Data Feeds**
- **WebSocket Integration**: Live market data updates
- **Order Updates**: Real-time order status changes
- **Market Data**: Live price feeds for subscribed symbols

### 🛡️ **Security Features**
- **Credential Encryption**: AES encryption for sensitive data
- **Session Security**: Secure token management
- **Auto-logout**: Session expiry handling

## 🔧 Installation & Setup

### 1. Install Backend Dependencies
```bash
cd backend
pip install cryptography==41.0.7
pip install -r requirements.txt
```

### 2. Install Frontend Dependencies
```bash
cd frontend
npm install
```

### 3. Database Setup
```bash
cd backend
python database/init_db.py create
```

### 4. Environment Configuration
Update `backend/.env`:
```env
# Encryption key for credentials (change in production)
ENCRYPTION_KEY=your-super-secret-encryption-key-change-in-production

# Database (SQLite for development)
DATABASE_URL=sqlite:///./niveshtor_ai.db

# Other settings...
SECRET_KEY=your-jwt-secret-key
```

## 🎯 How Users Connect to Angel One

### Step 1: Login to Your App
- Users login with demo credentials (demo/demo123)

### Step 2: Connect Angel One Account
- Click "Connect" in the SmartAPI Connection section
- Enter Angel One credentials:
  - **API Key**: From Angel One developer portal
  - **Client ID**: Your Angel One client ID
  - **Password**: Your Angel One password
  - **MPIN**: 4-digit trading PIN
  - **TOTP Secret**: From Angel One app settings

### Step 3: Start Trading
- Once connected, users can:
  - View real portfolio and holdings
  - Place live orders
  - Track positions and P&L
  - Get real-time market data

## 🔑 Angel One Credentials Required

Users need to obtain these from Angel One:

1. **API Key**: 
   - Register at Angel One Developer Portal
   - Create an app to get API key

2. **Client ID**: 
   - Your Angel One trading account ID

3. **Password**: 
   - Your Angel One login password

4. **MPIN**: 
   - 4-digit trading PIN set in Angel One app

5. **TOTP Secret**: 
   - Go to Angel One app → Settings → API
   - Generate TOTP secret key

## 🚀 Starting the Application

### Option 1: Use Startup Script
```bash
# Windows
start_dev.bat

# Or manually:
```

### Option 2: Manual Startup
```bash
# Terminal 1 - Backend
cd backend
python main_simple.py

# Terminal 2 - Frontend
cd frontend
npm run dev
```

## 🌐 Access URLs
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## 📱 Features Available

### 🔄 **Real-time Features**
- Live market data updates
- Real-time order status changes
- Portfolio value updates
- Position P&L tracking

### 💼 **Trading Features**
- Place market and limit orders
- View order history
- Track positions
- Portfolio management
- Watchlist with live prices

### 🛡️ **Security Features**
- Encrypted credential storage
- Secure session management
- Auto token refresh
- Connection status monitoring

## 🔧 API Endpoints

### SmartAPI Connection
- `POST /api/smartapi/connect` - Connect to Angel One
- `GET /api/smartapi/status` - Check connection status
- `POST /api/smartapi/disconnect` - Disconnect

### Trading
- `POST /api/trading/place-order` - Place orders
- `GET /api/trading/orders` - Get order history
- `GET /api/trading/positions` - Get positions

### Portfolio
- `GET /api/portfolio/holdings` - Get holdings
- `GET /api/portfolio/summary` - Portfolio summary

### WebSocket
- `ws://localhost:8000/api/ws/{user_id}` - Real-time data feed

## 🎉 Demo Credentials

For testing the app (without real trading):
- **Username**: demo
- **Password**: demo123

## ⚠️ Important Notes

1. **Security**: Never store credentials in plain text
2. **Testing**: Test with small amounts first
3. **Rate Limits**: Angel One has API rate limits
4. **Market Hours**: Trading only during market hours
5. **Compliance**: Ensure regulatory compliance

## 🐛 Troubleshooting

### Connection Issues
- Verify Angel One credentials
- Check API key validity
- Ensure TOTP secret is correct

### WebSocket Issues
- Check browser WebSocket support
- Verify authentication token
- Monitor network connectivity

### Database Issues
- Run database initialization
- Check file permissions
- Verify SQLite installation

## 📞 Support

For issues:
1. Check browser console for errors
2. Review backend logs
3. Verify Angel One API status
4. Test with demo mode first

---

**🎯 Your trading platform is now ready for real Angel One integration!**

Users can securely connect their Angel One accounts and start live trading through your web application.
