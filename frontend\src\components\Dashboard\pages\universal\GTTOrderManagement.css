/* GTT Order Management Styles */
.gtt-order-management {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* GTT Header */
.gtt-header {
  background: linear-gradient(135deg, var(--warning-50), var(--primary-50));
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--warning-200);
}

.connection-status {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* GTT Tabs */
.gtt-tabs {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.tab-navigation {
  display: flex;
  background: var(--secondary-50);
  border-radius: var(--radius-xl);
  padding: var(--space-2);
  gap: var(--space-2);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
  display: none;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  white-space: nowrap;
  font-weight: var(--font-medium);
  color: var(--secondary-600);
  min-width: fit-content;
}

.tab-button:hover {
  background: var(--secondary-100);
  color: var(--secondary-900);
}

.tab-button.active {
  background: white;
  color: var(--primary-600);
  box-shadow: var(--shadow-sm);
}

.tab-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
}

.tab-count {
  font-size: var(--text-xs);
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-content-card {
  min-height: 500px;
}

/* Tab Content */
.tab-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.tab-description {
  margin-bottom: var(--space-4);
}

.error-alert {
  margin-bottom: var(--space-4);
}

/* GTT Table */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
}

.gtt-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.gtt-table thead {
  background: var(--secondary-50);
}

.gtt-table th {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  border-bottom: 2px solid var(--secondary-200);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.gtt-table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--secondary-100);
  font-size: var(--text-sm);
  color: var(--secondary-700);
  white-space: nowrap;
}

.gtt-row {
  transition: all var(--transition-base);
}

.gtt-row:hover {
  background: var(--secondary-50);
}

/* Table Cell Styles */
.stock-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.stock-symbol {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  font-size: var(--text-sm);
}

.price-cell {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  text-align: right;
}

.quantity-cell {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  text-align: center;
}

.amount-cell {
  font-weight: var(--font-semibold);
  color: var(--success-600);
  text-align: right;
}

.status-cell {
  text-align: center;
}

.date-cell {
  font-family: var(--font-family-mono);
  font-size: var(--text-xs);
  color: var(--secondary-600);
}

.reason-cell {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  max-width: 150px;
  word-wrap: break-word;
}

.actions-cell {
  text-align: center;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
  min-height: 300px;
  justify-content: center;
}

.empty-icon {
  font-size: var(--text-4xl);
  color: var(--secondary-400);
}

.empty-state h4 {
  margin: 0;
  color: var(--secondary-700);
  font-size: var(--text-lg);
}

.empty-state p {
  margin: 0;
  color: var(--secondary-600);
}

/* Loading State */
.gtt-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--warning-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tab-navigation {
    justify-content: center;
  }
  
  .gtt-table th,
  .gtt-table td {
    padding: var(--space-3) var(--space-4);
  }
}

@media (max-width: 768px) {
  .gtt-header {
    padding: var(--space-4);
    text-align: center;
  }
  
  .tab-button {
    padding: var(--space-2) var(--space-3);
  }
  
  .tab-label {
    display: none;
  }
  
  .gtt-table {
    font-size: var(--text-xs);
  }
  
  .gtt-table th,
  .gtt-table td {
    padding: var(--space-2) var(--space-3);
  }
}

@media (max-width: 640px) {
  .tab-navigation {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .tab-button {
    justify-content: center;
  }
  
  .tab-label {
    display: block;
    font-size: var(--text-xs);
  }
  
  /* Hide less important columns on mobile */
  .gtt-table th:nth-child(6),
  .gtt-table td:nth-child(6),
  .gtt-table th:nth-child(7),
  .gtt-table td:nth-child(7) {
    display: none;
  }
}
