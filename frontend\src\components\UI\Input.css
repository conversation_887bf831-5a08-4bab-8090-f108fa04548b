/* Input Component */
.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.input-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--secondary-700);
  line-height: var(--leading-tight);
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-field {
  width: 100%;
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--secondary-900);
  background: white;
  border: 2px solid var(--secondary-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  outline: none;
}

.input-field:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-field::placeholder {
  color: var(--secondary-400);
  font-weight: var(--font-normal);
}

.input-field:disabled {
  background: var(--secondary-100);
  color: var(--secondary-500);
  cursor: not-allowed;
  border-color: var(--secondary-200);
}

/* Input Sizes */
.input-field--sm {
  height: 36px;
  padding: 0 var(--space-3);
  font-size: var(--text-sm);
}

.input-field--md {
  height: var(--input-height);
  padding: 0 var(--space-4);
  font-size: var(--text-base);
}

.input-field--lg {
  height: 52px;
  padding: 0 var(--space-5);
  font-size: var(--text-lg);
}

/* Input Variants */
.input-field--default {
  border-color: var(--secondary-300);
  background: white;
}

.input-field--filled {
  border-color: transparent;
  background: var(--secondary-100);
}

.input-field--filled:focus {
  background: white;
  border-color: var(--primary-500);
}

.input-field--outlined {
  border-width: 2px;
  border-color: var(--secondary-400);
  background: transparent;
}

/* Input States */
.input-field--error {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-field--error:focus {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-field--full-width {
  width: 100%;
}

/* Input with Icons */
.input-field--with-icon-left {
  padding-left: var(--space-12);
}

.input-field--with-icon-right {
  padding-right: var(--space-12);
}

.input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-400);
  pointer-events: none;
  z-index: 1;
}

.input-icon--left {
  left: var(--space-3);
}

.input-icon--right {
  right: var(--space-3);
}

.input-icon svg {
  width: 20px;
  height: 20px;
}

/* Input Feedback */
.input-feedback {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.input-error {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--error-600);
  line-height: var(--leading-tight);
}

.input-helper {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--secondary-500);
  line-height: var(--leading-tight);
}

/* Focus States */
.input-field:focus + .input-icon {
  color: var(--primary-500);
}

.input-field--error:focus + .input-icon {
  color: var(--error-500);
}

/* Responsive Design */
@media (max-width: 640px) {
  .input-field--sm {
    height: 32px;
    padding: 0 var(--space-2);
    font-size: var(--text-xs);
  }
  
  .input-field--md {
    height: 40px;
    padding: 0 var(--space-3);
    font-size: var(--text-sm);
  }
  
  .input-field--lg {
    height: 48px;
    padding: 0 var(--space-4);
    font-size: var(--text-base);
  }
  
  .input-field--with-icon-left {
    padding-left: var(--space-10);
  }
  
  .input-field--with-icon-right {
    padding-right: var(--space-10);
  }
  
  .input-icon--left {
    left: var(--space-2);
  }
  
  .input-icon--right {
    right: var(--space-2);
  }
}
