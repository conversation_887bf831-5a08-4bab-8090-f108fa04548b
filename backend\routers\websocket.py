from fastapi import API<PERSON>outer, WebSocket, WebSocketDisconnect, Depends, Query
from sqlalchemy.orm import Session
from database.database import get_db
from services.websocket_service import websocket_manager
from services.smartapi_service import smartapi_manager
from services.credential_service import credential_service
import json
import logging
from typing import Optional

logger = logging.getLogger(__name__)

router = APIRouter()

@router.websocket("/ws/{user_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    user_id: str,
    token: Optional[str] = Query(None)
):
    """WebSocket endpoint for real-time data"""
    try:
        # In a real implementation, validate the token here
        if not token:
            await websocket.close(code=4001, reason="Authentication required")
            return
        
        # Connect WebSocket
        await websocket_manager.connect(websocket, user_id)
        
        # Get database session
        db = next(get_db())
        
        try:
            # Check if user has active SmartAPI session
            active_session = credential_service.get_active_session(db, user_id)
            
            if active_session:
                # Setup SmartAPI WebSocket
                success = websocket_manager.setup_smartapi_websocket(
                    user_id=user_id,
                    auth_token=active_session.get('auth_token'),
                    feed_token=active_session.get('feed_token'),
                    client_id="demo_client"  # This should come from credentials
                )
                
                if success:
                    await websocket_manager.send_personal_message({
                        'type': 'connection_status',
                        'status': 'connected',
                        'message': 'Real-time data feed connected'
                    }, user_id)
                else:
                    await websocket_manager.send_personal_message({
                        'type': 'connection_status',
                        'status': 'error',
                        'message': 'Failed to connect to real-time data feed'
                    }, user_id)
            else:
                await websocket_manager.send_personal_message({
                    'type': 'connection_status',
                    'status': 'not_authenticated',
                    'message': 'Please connect to Angel One first'
                }, user_id)
            
            # Listen for messages
            while True:
                try:
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Handle different message types
                    if message.get('type') == 'subscribe':
                        symbols = message.get('symbols', [])
                        await websocket_manager.subscribe_to_symbols(user_id, symbols)
                    
                    elif message.get('type') == 'unsubscribe':
                        symbols = message.get('symbols', [])
                        await websocket_manager.unsubscribe_from_symbols(user_id, symbols)
                    
                    elif message.get('type') == 'ping':
                        await websocket_manager.send_personal_message({
                            'type': 'pong',
                            'timestamp': message.get('timestamp')
                        }, user_id)
                    
                    else:
                        await websocket_manager.send_personal_message({
                            'type': 'error',
                            'message': f'Unknown message type: {message.get("type")}'
                        }, user_id)
                
                except WebSocketDisconnect:
                    break
                except json.JSONDecodeError:
                    await websocket_manager.send_personal_message({
                        'type': 'error',
                        'message': 'Invalid JSON format'
                    }, user_id)
                except Exception as e:
                    logger.error(f"Error processing WebSocket message for user {user_id}: {e}")
                    await websocket_manager.send_personal_message({
                        'type': 'error',
                        'message': 'Message processing failed'
                    }, user_id)
        
        finally:
            db.close()
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user {user_id}")
    except Exception as e:
        logger.error(f"WebSocket error for user {user_id}: {e}")
    finally:
        websocket_manager.disconnect(user_id)

@router.get("/ws/status")
async def websocket_status():
    """Get WebSocket connection status"""
    return {
        "active_connections": len(websocket_manager.active_connections),
        "connected_users": list(websocket_manager.active_connections.keys())
    }
