"""
Service for managing SmartAPI credentials securely
"""

from sqlalchemy.orm import Session
from database.models.smartapi_credentials import SmartAPICredentials, SmartAPISession
from services.encryption_service import encryption_service
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class CredentialService:
    
    @staticmethod
    def save_credentials(db: Session, user_id: int, credentials: Dict[str, str]) -> Dict[str, Any]:
        """Save encrypted SmartAPI credentials for a user"""
        try:
            # Encrypt sensitive credentials
            encrypted_creds = encryption_service.encrypt_credentials(credentials)
            
            # Check if credentials already exist
            existing_creds = db.query(SmartAPICredentials).filter(
                SmartAPICredentials.user_id == user_id
            ).first()
            
            if existing_creds:
                # Update existing credentials
                existing_creds.api_key_encrypted = encrypted_creds.get('api_key_encrypted')
                existing_creds.client_id = credentials.get('client_id')
                existing_creds.password_encrypted = encrypted_creds.get('password_encrypted')
                existing_creds.mpin_encrypted = encrypted_creds.get('mpin_encrypted')
                existing_creds.totp_encrypted = encrypted_creds.get('totp_encrypted')
                existing_creds.updated_at = datetime.now()
                existing_creds.is_connected = False  # Reset connection status
                existing_creds.connection_error = None
            else:
                # Create new credentials
                existing_creds = SmartAPICredentials(
                    user_id=user_id,
                    api_key_encrypted=encrypted_creds.get('api_key_encrypted'),
                    client_id=credentials.get('client_id'),
                    password_encrypted=encrypted_creds.get('password_encrypted'),
                    mpin_encrypted=encrypted_creds.get('mpin_encrypted'),
                    totp_encrypted=encrypted_creds.get('totp_encrypted')
                )
                db.add(existing_creds)
            
            db.commit()
            db.refresh(existing_creds)
            
            return {
                "success": True,
                "message": "Credentials saved successfully"
            }
            
        except Exception as e:
            logger.error(f"Error saving credentials for user {user_id}: {e}")
            db.rollback()
            return {
                "success": False,
                "error": "Failed to save credentials"
            }
    
    @staticmethod
    def get_credentials(db: Session, user_id: int) -> Optional[Dict[str, str]]:
        """Get decrypted SmartAPI credentials for a user"""
        try:
            creds = db.query(SmartAPICredentials).filter(
                SmartAPICredentials.user_id == user_id
            ).first()
            
            if not creds:
                return None
            
            # Decrypt credentials
            encrypted_data = {
                'api_key_encrypted': creds.api_key_encrypted,
                'password_encrypted': creds.password_encrypted,
                'mpin_encrypted': creds.mpin_encrypted,
                'totp_encrypted': creds.totp_encrypted,
                'client_id': creds.client_id
            }
            
            decrypted_creds = encryption_service.decrypt_credentials(encrypted_data)
            
            return decrypted_creds
            
        except Exception as e:
            logger.error(f"Error getting credentials for user {user_id}: {e}")
            return None
    
    @staticmethod
    def update_connection_status(db: Session, user_id: int, is_connected: bool, 
                               error_message: str = None) -> bool:
        """Update connection status for user credentials"""
        try:
            creds = db.query(SmartAPICredentials).filter(
                SmartAPICredentials.user_id == user_id
            ).first()
            
            if creds:
                creds.is_connected = is_connected
                creds.last_connected = datetime.now() if is_connected else creds.last_connected
                creds.connection_error = error_message
                db.commit()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating connection status for user {user_id}: {e}")
            db.rollback()
            return False
    
    @staticmethod
    def save_session(db: Session, user_id: int, session_data: Dict[str, Any]) -> bool:
        """Save encrypted session tokens"""
        try:
            # Encrypt session tokens
            encrypted_tokens = {}
            if session_data.get('auth_token'):
                encrypted_tokens['auth_token_encrypted'] = encryption_service.encrypt(session_data['auth_token'])
            if session_data.get('feed_token'):
                encrypted_tokens['feed_token_encrypted'] = encryption_service.encrypt(session_data['feed_token'])
            if session_data.get('refresh_token'):
                encrypted_tokens['refresh_token_encrypted'] = encryption_service.encrypt(session_data['refresh_token'])
            
            # Deactivate existing sessions
            db.query(SmartAPISession).filter(
                SmartAPISession.user_id == user_id,
                SmartAPISession.is_active == True
            ).update({"is_active": False})
            
            # Create new session
            new_session = SmartAPISession(
                user_id=user_id,
                auth_token_encrypted=encrypted_tokens.get('auth_token_encrypted'),
                feed_token_encrypted=encrypted_tokens.get('feed_token_encrypted'),
                refresh_token_encrypted=encrypted_tokens.get('refresh_token_encrypted'),
                session_expiry=session_data.get('session_expiry'),
                user_name=session_data.get('user_name'),
                broker=session_data.get('broker')
            )
            
            db.add(new_session)
            db.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"Error saving session for user {user_id}: {e}")
            db.rollback()
            return False
    
    @staticmethod
    def get_active_session(db: Session, user_id: int) -> Optional[Dict[str, str]]:
        """Get active decrypted session tokens"""
        try:
            session = db.query(SmartAPISession).filter(
                SmartAPISession.user_id == user_id,
                SmartAPISession.is_active == True,
                SmartAPISession.session_expiry > datetime.now()
            ).first()
            
            if not session:
                return None
            
            # Decrypt session tokens
            decrypted_session = {}
            
            if session.auth_token_encrypted:
                decrypted_session['auth_token'] = encryption_service.decrypt(session.auth_token_encrypted)
            if session.feed_token_encrypted:
                decrypted_session['feed_token'] = encryption_service.decrypt(session.feed_token_encrypted)
            if session.refresh_token_encrypted:
                decrypted_session['refresh_token'] = encryption_service.decrypt(session.refresh_token_encrypted)
            
            decrypted_session.update({
                'session_expiry': session.session_expiry,
                'user_name': session.user_name,
                'broker': session.broker
            })
            
            return decrypted_session
            
        except Exception as e:
            logger.error(f"Error getting active session for user {user_id}: {e}")
            return None
    
    @staticmethod
    def delete_credentials(db: Session, user_id: int) -> bool:
        """Delete user credentials and sessions"""
        try:
            # Delete credentials
            db.query(SmartAPICredentials).filter(
                SmartAPICredentials.user_id == user_id
            ).delete()
            
            # Delete sessions
            db.query(SmartAPISession).filter(
                SmartAPISession.user_id == user_id
            ).delete()
            
            db.commit()
            return True
            
        except Exception as e:
            logger.error(f"Error deleting credentials for user {user_id}: {e}")
            db.rollback()
            return False

# Global credential service instance
credential_service = CredentialService()
