import React from 'react';
import './StrategyComingSoon.css';

const ConsolidatedBreakoutStrategy = ({ userSubscription, hasAccess, onNavigate }) => {
  const hasBreakoutAccess = hasAccess('enterprise');

  if (!hasBreakoutAccess) {
    return (
      <div className="strategy-locked">
        <div className="locked-content">
          <div className="lock-icon">🔒</div>
          <h1>Consolidated Breakout Strategy</h1>
          <p>Multi-timeframe analysis for high-probability breakout trades</p>
          
          <div className="feature-preview">
            <h3>What you'll get with Enterprise Plan:</h3>
            <ul className="feature-list">
              <li>🎯 Multi-timeframe consolidation detection</li>
              <li>📊 Volume-based breakout confirmation</li>
              <li>⚡ High-probability entry signals</li>
              <li>🔄 Advanced pattern recognition</li>
              <li>📈 Custom strategy parameters</li>
              <li>🛠️ API access for automation</li>
              <li>👨‍💼 Dedicated support</li>
              <li>🏷️ White-label solution</li>
            </ul>
          </div>

          <div className="upgrade-section">
            <h3>Upgrade to Enterprise</h3>
            <div className="pricing-highlight">
              <div className="price">₹12,999<span>/month</span></div>
              <div className="savings">Save 17% with yearly plan</div>
            </div>
            
            <button 
              className="upgrade-btn enterprise"
              onClick={() => onNavigate('upgrade-subscription')}
            >
              Upgrade to Enterprise
            </button>
            
            <div className="upgrade-benefits">
              <p>✓ All Professional features included</p>
              <p>✓ Consolidated Breakout Strategy</p>
              <p>✓ API access & automation</p>
              <p>✓ White-label solution</p>
              <p>✓ Dedicated support</p>
              <p>✓ Custom strategies</p>
            </div>
          </div>
        </div>
        
        <div className="preview-dashboard">
          <div className="preview-header">
            <h4>Consolidated Breakout Dashboard Preview</h4>
            <span className="preview-badge enterprise">Enterprise Feature</span>
          </div>
          
          <div className="preview-content">
            <div className="preview-metrics">
              <div className="preview-metric">
                <span className="metric-label">Breakout Signals</span>
                <span className="metric-value">8</span>
              </div>
              <div className="preview-metric">
                <span className="metric-label">Success Rate</span>
                <span className="metric-value">92%</span>
              </div>
              <div className="preview-metric">
                <span className="metric-label">Avg Return</span>
                <span className="metric-value">+8.5%</span>
              </div>
            </div>
            
            <div className="preview-signals">
              <div className="preview-signal">
                <div className="signal-info">
                  <span className="signal-symbol">BAJFINANCE</span>
                  <span className="signal-action buy">BREAKOUT</span>
                </div>
                <div className="signal-details">
                  <span>Consolidation: 15 days</span>
                  <span>Volume: 2.5x avg</span>
                  <span>Confidence: 95%</span>
                </div>
              </div>
              
              <div className="preview-signal">
                <div className="signal-info">
                  <span className="signal-symbol">ASIANPAINT</span>
                  <span className="signal-action buy">BREAKOUT</span>
                </div>
                <div className="signal-details">
                  <span>Consolidation: 8 days</span>
                  <span>Volume: 3.2x avg</span>
                  <span>Confidence: 88%</span>
                </div>
              </div>
            </div>
            
            <div className="preview-chart">
              <div className="chart-header">
                <span>Multi-timeframe Analysis</span>
                <span className="chart-status">Live</span>
              </div>
              <div className="chart-placeholder">
                <div className="chart-lines">
                  <div className="chart-line"></div>
                  <div className="chart-line"></div>
                  <div className="chart-line"></div>
                </div>
                <div className="breakout-points">
                  <div className="breakout-point"></div>
                  <div className="breakout-point"></div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="preview-overlay">
            <div className="overlay-content">
              <div className="overlay-icon">🔒</div>
              <p>Upgrade to Enterprise to unlock</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user has access, show the actual strategy component
  return (
    <div className="consolidated-breakout-strategy">
      <div className="page-header">
        <h1>Consolidated Breakout Strategy</h1>
        <p>Multi-timeframe analysis for high-probability breakout trades</p>
      </div>
      
      <div className="coming-soon-notice">
        <div className="notice-content">
          <h2>🚧 Coming Soon</h2>
          <p>The Consolidated Breakout Strategy is currently under development and will be available soon.</p>
          <div className="eta">
            <strong>Expected Release:</strong> Q3 2024
          </div>
        </div>
      </div>
      
      {/* Placeholder for future implementation */}
      <div className="strategy-placeholder">
        <div className="placeholder-metrics">
          <div className="placeholder-card">
            <h3>Breakout Signals</h3>
            <div className="placeholder-value">Coming Soon</div>
          </div>
          <div className="placeholder-card">
            <h3>Pattern Recognition</h3>
            <div className="placeholder-value">Coming Soon</div>
          </div>
          <div className="placeholder-card">
            <h3>Multi-timeframe Analysis</h3>
            <div className="placeholder-value">Coming Soon</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsolidatedBreakoutStrategy;
