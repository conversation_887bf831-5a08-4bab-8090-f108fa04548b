/* Sidebar Styles */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 280px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  z-index: 1000;
  overflow: hidden;
}

.sidebar.collapsed {
  width: 80px;
}

/* Sidebar Header */
.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.sidebar-brand h2 {
  margin: 0;
  color: #1e40af;
  font-weight: 700;
  font-size: 1.5rem;
}

.brand-subtitle {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 400;
  margin-top: 0.25rem;
}

.brand-icon {
  font-size: 2rem;
}

.sidebar-toggle {
  background: #f3f4f6;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Subscription Info */
.subscription-info {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.subscription-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.subscription-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.subscription-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.subscription-badge.current {
  background: #10b981;
}

.subscription-status {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

.subscription-description {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0 0 0.75rem;
  line-height: 1.4;
}

.upgrade-button {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upgrade-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-group {
  margin-bottom: 0.5rem;
}

.nav-item, .nav-group-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.nav-item:hover, .nav-group-header:hover {
  background: #f3f4f6;
  color: #1e40af;
}

.nav-item.active {
  background: #eff6ff;
  color: #1e40af;
  border-right: 3px solid #3b82f6;
}

.nav-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.nav-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  width: 24px;
  text-align: center;
}

.nav-label {
  flex: 1;
  font-weight: 500;
}

.nav-arrow {
  font-size: 0.75rem;
  transition: transform 0.3s ease;
}

.nav-arrow.rotated {
  transform: rotate(180deg);
}

/* Submenu */
.nav-submenu {
  background: #f8fafc;
  border-left: 2px solid #e5e7eb;
  margin-left: 1.5rem;
}

.nav-subitem {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem 0.5rem 2rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.nav-subitem:hover {
  background: #f3f4f6;
  color: #374151;
}

.nav-subitem.active {
  background: #eff6ff;
  color: #1e40af;
}

.nav-subitem.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.nav-subitem-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.nav-subicon {
  font-size: 1rem;
  margin-right: 0.5rem;
  width: 20px;
  text-align: center;
}

.nav-subitem-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nav-sublabel {
  font-size: 0.9rem;
  font-weight: 500;
}

.coming-soon-badge {
  background: #f59e0b;
  color: white;
  font-size: 0.65rem;
  padding: 0.125rem 0.5rem;
  border-radius: 8px;
  font-weight: 600;
  width: fit-content;
}

.lock-icon {
  font-size: 0.9rem;
  color: #9ca3af;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.quick-action-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.action-icon {
  font-size: 1rem;
}

.action-label {
  font-weight: 500;
  color: #374151;
}

/* Collapsed State */
.sidebar.collapsed .sidebar-brand h2,
.sidebar.collapsed .brand-subtitle,
.sidebar.collapsed .subscription-info,
.sidebar.collapsed .nav-label,
.sidebar.collapsed .nav-arrow,
.sidebar.collapsed .nav-submenu,
.sidebar.collapsed .sidebar-footer {
  display: none;
}

.sidebar.collapsed .nav-item,
.sidebar.collapsed .nav-group-header {
  justify-content: center;
  padding: 0.75rem;
}

.sidebar.collapsed .nav-icon {
  margin-right: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .sidebar.collapsed {
    width: 280px;
    transform: translateX(-100%);
  }
}
