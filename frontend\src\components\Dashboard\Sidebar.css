/* Modern Sidebar Styles */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: var(--sidebar-width);
  background: white;
  border-right: 1px solid var(--secondary-200);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-base);
  z-index: var(--z-fixed);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

/* Modern Sidebar Header */
.sidebar-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--secondary-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: var(--header-height);
  background: linear-gradient(135deg, var(--primary-50), white);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.brand-icon {
  font-size: var(--text-2xl);
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-brand h2 {
  margin: 0;
  color: var(--secondary-900);
  font-weight: var(--font-bold);
  font-size: var(--text-xl);
  line-height: var(--leading-tight);
}

.brand-subtitle {
  display: block;
  font-size: var(--text-xs);
  color: var(--secondary-500);
  font-weight: var(--font-medium);
  margin-top: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sidebar-toggle {
  background: var(--secondary-100);
  border: 1px solid var(--secondary-200);
  width: 36px;
  height: 36px;
  border-radius: var(--radius-lg);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-600);
  transition: all var(--transition-base);
}

.sidebar-toggle:hover {
  background: var(--secondary-200);
  color: var(--secondary-700);
  transform: scale(1.05);
}

/* Modern Subscription Info */
.subscription-info {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--secondary-200);
}

.subscription-card {
  background: linear-gradient(135deg, var(--secondary-50), white);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
  position: relative;
  overflow: hidden;
}

.subscription-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-500), var(--success-500));
}

.subscription-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.subscription-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.subscription-badge.current {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  box-shadow: var(--shadow-sm);
}

.subscription-status {
  font-size: var(--text-xs);
  color: var(--success-600);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.subscription-description {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  margin: 0 0 var(--space-4);
  line-height: var(--leading-relaxed);
}

.upgrade-button {
  width: 100%;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.upgrade-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
}

/* Modern Navigation */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.nav-group {
  margin-bottom: var(--space-2);
}

.nav-item, .nav-group-header {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-6);
  color: var(--secondary-700);
  cursor: pointer;
  transition: all var(--transition-base);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
  margin-right: var(--space-2);
  position: relative;
}

.nav-item:hover, .nav-group-header:hover {
  background: var(--primary-50);
  color: var(--primary-700);
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-50));
  color: var(--primary-700);
  border-right: 3px solid var(--primary-600);
  box-shadow: var(--shadow-sm);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--primary-600);
  border-radius: 0 var(--radius-base) var(--radius-base) 0;
}

.nav-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.nav-icon {
  font-size: 1.25rem;
  margin-right: 0.75rem;
  width: 24px;
  text-align: center;
}

.nav-label {
  flex: 1;
  font-weight: 500;
}

.nav-arrow {
  font-size: 0.75rem;
  transition: transform 0.3s ease;
}

.nav-arrow.rotated {
  transform: rotate(180deg);
}

/* Submenu */
.nav-submenu {
  background: #f8fafc;
  border-left: 2px solid #e5e7eb;
  margin-left: 1.5rem;
}

.nav-subitem {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem 0.5rem 2rem;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.nav-subitem:hover {
  background: #f3f4f6;
  color: #374151;
}

.nav-subitem.active {
  background: #eff6ff;
  color: #1e40af;
}

.nav-subitem.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.nav-subitem-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.nav-subicon {
  font-size: 1rem;
  margin-right: 0.5rem;
  width: 20px;
  text-align: center;
}

.nav-subitem-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nav-sublabel {
  font-size: 0.9rem;
  font-weight: 500;
}

.coming-soon-badge {
  background: #f59e0b;
  color: white;
  font-size: 0.65rem;
  padding: 0.125rem 0.5rem;
  border-radius: 8px;
  font-weight: 600;
  width: fit-content;
}

.lock-icon {
  font-size: 0.9rem;
  color: #9ca3af;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.quick-action-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.action-icon {
  font-size: 1rem;
}

.action-label {
  font-weight: 500;
  color: #374151;
}

/* Collapsed State */
.sidebar.collapsed .sidebar-brand h2,
.sidebar.collapsed .brand-subtitle,
.sidebar.collapsed .subscription-info,
.sidebar.collapsed .nav-label,
.sidebar.collapsed .nav-arrow,
.sidebar.collapsed .nav-submenu,
.sidebar.collapsed .sidebar-footer {
  display: none;
}

.sidebar.collapsed .nav-item,
.sidebar.collapsed .nav-group-header {
  justify-content: center;
  padding: 0.75rem;
}

.sidebar.collapsed .nav-icon {
  margin-right: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.mobile-open {
    transform: translateX(0);
  }
  
  .sidebar.collapsed {
    width: 280px;
    transform: translateX(-100%);
  }
}
