/* Capital Management Styles */
.capital-management {
  padding: 0;
  background: var(--secondary-50);
}

/* Tab Navigation */
.capital-tabs {
  background: white;
  border-radius: var(--radius-2xl);
  border: 1px solid var(--secondary-200);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.tab-navigation {
  display: flex;
  background: var(--secondary-50);
  border-bottom: 1px solid var(--secondary-200);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
  display: none;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  white-space: nowrap;
  font-weight: var(--font-medium);
  color: var(--secondary-600);
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: var(--secondary-100);
  color: var(--secondary-900);
}

.tab-button.active {
  background: white;
  color: var(--primary-600);
  border-bottom-color: var(--primary-500);
}

.tab-icon {
  font-size: var(--text-lg);
}

.tab-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
}

.tab-content {
  padding: var(--space-8);
  min-height: 600px;
}

/* Capital Overview */
.capital-overview {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.capital-summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
}

.capital-card {
  transition: all var(--transition-base);
}

.capital-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.capital-metric {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.metric-icon {
  font-size: var(--text-3xl);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-50), var(--success-50));
  border-radius: var(--radius-xl);
  border: 2px solid var(--primary-100);
}

.metric-details {
  flex: 1;
}

.metric-details .card-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin: var(--space-1) 0;
}

.text-success {
  color: var(--success-600);
}

.text-primary {
  color: var(--primary-600);
}

/* Capital Rules Section */
.capital-rules-section .card {
  background: linear-gradient(135deg, var(--primary-50), var(--success-50));
  border: 1px solid var(--primary-200);
}

.rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.rule-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--secondary-200);
  transition: all var(--transition-base);
}

.rule-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.rule-icon {
  font-size: var(--text-2xl);
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--radius-lg);
}

.rule-content h4 {
  margin: 0 0 var(--space-1);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
}

.rule-content p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
}

/* Strategy Status Section */
.strategy-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.strategy-status-item {
  padding: var(--space-6);
  background: var(--secondary-50);
  border-radius: var(--radius-xl);
  border: 1px solid var(--secondary-200);
  transition: all var(--transition-base);
}

.strategy-status-item:hover {
  background: white;
  box-shadow: var(--shadow-lg);
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.strategy-header h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  text-transform: capitalize;
}

.strategy-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
}

.metric span {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--secondary-700);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .capital-summary-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .rules-grid {
    grid-template-columns: 1fr;
  }
  
  .strategy-status-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .tab-content {
    padding: var(--space-4);
  }
  
  .capital-summary-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-button {
    padding: var(--space-3) var(--space-4);
  }
  
  .tab-label {
    display: none;
  }
  
  .rule-item {
    flex-direction: column;
    text-align: center;
  }
  
  .metric-icon {
    width: 50px;
    height: 50px;
    font-size: var(--text-2xl);
  }
}

@media (max-width: 640px) {
  .capital-metric {
    flex-direction: column;
    text-align: center;
    gap: var(--space-3);
  }
  
  .strategy-header {
    flex-direction: column;
    gap: var(--space-2);
    align-items: stretch;
  }
}
