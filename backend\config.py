from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = "postgresql://username:password@localhost:5432/niveshtor_ai"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # SmartAPI Configuration
    SMARTAPI_API_KEY: Optional[str] = None
    SMARTAPI_CLIENT_ID: Optional[str] = None
    SMARTAPI_PASSWORD: Optional[str] = None
    SMARTAPI_TOTP: Optional[str] = None
    
    # Yahoo Finance
    YAHOO_FINANCE_ENABLED: bool = True
    
    # API Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # CORS
    ALLOWED_ORIGINS: list = ["http://localhost:5173", "http://localhost:3000"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Database URL for different environments
def get_database_url():
    if settings.ENVIRONMENT == "production":
        return settings.DATABASE_URL
    else:
        # Development database
        return "postgresql://postgres:password@localhost:5432/niveshtor_ai_dev"
