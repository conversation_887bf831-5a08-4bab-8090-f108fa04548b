try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = "sqlite:///./niveshtor_ai.db"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # SmartAPI Configuration
    SMARTAPI_API_KEY: Optional[str] = None
    SMARTAPI_CLIENT_ID: Optional[str] = None
    SMARTAPI_PASSWORD: Optional[str] = None
    SMARTAPI_TOTP: Optional[str] = None
    
    # Yahoo Finance
    YAHOO_FINANCE_ENABLED: bool = True
    
    # API Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    
    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # CORS
    ALLOWED_ORIGINS: list = ["http://localhost:5173", "http://localhost:3000"]
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Database URL for different environments
def get_database_url():
    if settings.ENVIRONMENT == "production":
        return settings.DATABASE_URL
    else:
        # Development database (SQLite)
        return "sqlite:///./niveshtor_ai_dev.db"
