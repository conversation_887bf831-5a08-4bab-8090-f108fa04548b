import React, { useState, useEffect } from 'react';
import { Card, Button, Badge, Input } from '../../../UI';
import { fetchNifty200StockData } from '../../../../services/yahooFinanceService';
import './BOHFilter.css';

const BOHFilter = () => {
  const [stocks, setStocks] = useState([]);
  const [filteredStocks, setFilteredStocks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCriteria, setFilterCriteria] = useState({
    bohEligible: 'all', // 'all', 'yes', 'no'
    priceRange: 'all', // 'all', 'under2000', 'over2000'
    sortBy: 'stockName' // 'stockName', 'cmp', 'bohEligible'
  });

  // Fetch stock data on component mount
  useEffect(() => {
    fetchStockData();
  }, []);

  // Filter stocks based on search and criteria
  useEffect(() => {
    filterStocks();
  }, [stocks, searchTerm, filterCriteria]);

  const fetchStockData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch Nifty 200 stock data with 52-week high/low information
      const stockData = await fetchNifty200StockData();
      
      // Process each stock to determine BOH eligibility
      const processedStocks = stockData.map(stock => ({
        ...stock,
        bohEligible: determineBOHEligibility(stock)
      }));
      
      setStocks(processedStocks);
    } catch (err) {
      setError('Failed to fetch stock data. Please try again.');
      console.error('BOH Filter fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Critical BOH Eligibility Logic: 52-week low date > 52-week high date
  const determineBOHEligibility = (stock) => {
    if (!stock.fiftyTwoWeekLowDate || !stock.fiftyTwoWeekHighDate) {
      return false;
    }
    
    const lowDate = new Date(stock.fiftyTwoWeekLowDate);
    const highDate = new Date(stock.fiftyTwoWeekHighDate);
    
    // BOH Eligible = TRUE only if 52-week low occurred AFTER 52-week high
    // This identifies the "Boom → Bust → Recovery" pattern
    return lowDate > highDate;
  };

  const filterStocks = () => {
    let filtered = [...stocks];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(stock =>
        stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // BOH Eligibility filter
    if (filterCriteria.bohEligible !== 'all') {
      const isEligible = filterCriteria.bohEligible === 'yes';
      filtered = filtered.filter(stock => stock.bohEligible === isEligible);
    }

    // Price range filter
    if (filterCriteria.priceRange !== 'all') {
      if (filterCriteria.priceRange === 'under2000') {
        filtered = filtered.filter(stock => stock.currentPrice < 2000);
      } else if (filterCriteria.priceRange === 'over2000') {
        filtered = filtered.filter(stock => stock.currentPrice >= 2000);
      }
    }

    // Sort stocks
    filtered.sort((a, b) => {
      switch (filterCriteria.sortBy) {
        case 'cmp':
          return b.currentPrice - a.currentPrice;
        case 'bohEligible':
          return b.bohEligible - a.bohEligible;
        default:
          return a.symbol.localeCompare(b.symbol);
      }
    });

    setFilteredStocks(filtered);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatPrice = (price) => {
    return `₹${price?.toFixed(2) || '0.00'}`;
  };

  if (loading) {
    return (
      <div className="boh-filter-loading">
        <Card variant="elevated">
          <Card.Content>
            <div className="loading-content">
              <div className="loading-spinner"></div>
              <p>Fetching Nifty 200 stock data and calculating BOH eligibility...</p>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="boh-filter-error">
        <Card variant="elevated">
          <Card.Content>
            <div className="error-content">
              <div className="error-icon">⚠️</div>
              <h3>Error Loading Data</h3>
              <p>{error}</p>
              <Button onClick={fetchStockData} variant="primary">
                Retry
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="boh-filter">
      <div className="boh-filter-header">
        <div className="filter-summary">
          <h3>BOH (Boom-Bust-Recovery) Filter</h3>
          <p>Identifying stocks with 52-week low occurring after 52-week high</p>
          <div className="summary-stats">
            <Badge variant="primary">
              Total Stocks: {stocks.length}
            </Badge>
            <Badge variant="success">
              BOH Eligible: {stocks.filter(s => s.bohEligible).length}
            </Badge>
            <Badge variant="secondary">
              Under ₹2000: {stocks.filter(s => s.currentPrice < 2000).length}
            </Badge>
          </div>
        </div>
      </div>

      <div className="boh-filter-controls">
        <Card variant="elevated">
          <Card.Content>
            <div className="filter-controls-grid">
              <div className="search-control">
                <Input
                  type="text"
                  placeholder="Search stocks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="filter-control">
                <label>BOH Eligibility</label>
                <select
                  value={filterCriteria.bohEligible}
                  onChange={(e) => setFilterCriteria(prev => ({
                    ...prev,
                    bohEligible: e.target.value
                  }))}
                >
                  <option value="all">All Stocks</option>
                  <option value="yes">BOH Eligible Only</option>
                  <option value="no">Not BOH Eligible</option>
                </select>
              </div>

              <div className="filter-control">
                <label>Price Range</label>
                <select
                  value={filterCriteria.priceRange}
                  onChange={(e) => setFilterCriteria(prev => ({
                    ...prev,
                    priceRange: e.target.value
                  }))}
                >
                  <option value="all">All Prices</option>
                  <option value="under2000">Under ₹2000</option>
                  <option value="over2000">₹2000 & Above</option>
                </select>
              </div>

              <div className="filter-control">
                <label>Sort By</label>
                <select
                  value={filterCriteria.sortBy}
                  onChange={(e) => setFilterCriteria(prev => ({
                    ...prev,
                    sortBy: e.target.value
                  }))}
                >
                  <option value="stockName">Stock Name</option>
                  <option value="cmp">Price (High to Low)</option>
                  <option value="bohEligible">BOH Eligibility</option>
                </select>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="boh-filter-table">
        <Card variant="elevated">
          <Card.Header>
            <Card.Title>BOH Filter Results ({filteredStocks.length} stocks)</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="table-container">
              <table className="boh-table">
                <thead>
                  <tr>
                    <th>Stock Name</th>
                    <th>CMP</th>
                    <th>52-Week Low Date</th>
                    <th>52-Week High Date</th>
                    <th>BOH Eligible</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStocks.map((stock, index) => (
                    <tr key={stock.symbol} className={stock.bohEligible ? 'boh-eligible' : ''}>
                      <td>
                        <div className="stock-info">
                          <span className="stock-symbol">{stock.symbol}</span>
                          <span className="stock-name">{stock.name}</span>
                        </div>
                      </td>
                      <td className="price-cell">
                        {formatPrice(stock.currentPrice)}
                      </td>
                      <td className="date-cell">
                        {formatDate(stock.fiftyTwoWeekLowDate)}
                      </td>
                      <td className="date-cell">
                        {formatDate(stock.fiftyTwoWeekHighDate)}
                      </td>
                      <td className="eligibility-cell">
                        <Badge variant={stock.bohEligible ? 'success' : 'secondary'}>
                          {stock.bohEligible ? 'Yes' : 'No'}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
};

export default BOHFilter;
