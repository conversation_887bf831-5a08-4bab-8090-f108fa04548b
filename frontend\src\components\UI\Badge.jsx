import React from 'react';
import './Badge.css';

const Badge = ({
  children,
  variant = 'default',
  size = 'md',
  shape = 'rounded',
  className = '',
  ...props
}) => {
  const baseClasses = 'badge';
  const variantClasses = `badge--${variant}`;
  const sizeClasses = `badge--${size}`;
  const shapeClasses = `badge--${shape}`;

  const badgeClasses = [
    baseClasses,
    variantClasses,
    sizeClasses,
    shapeClasses,
    className
  ].filter(Boolean).join(' ');

  return (
    <span className={badgeClasses} {...props}>
      {children}
    </span>
  );
};

export default Badge;
