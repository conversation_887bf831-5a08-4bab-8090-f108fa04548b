import React, { useState } from 'react';
import LoginForm from './LoginForm';
import SignupForm from './SignupForm';
import ForgotPasswordForm from './ForgotPasswordForm';
import './AuthContainer.css';

const AuthContainer = ({ onLogin }) => {
  const [activeForm, setActiveForm] = useState('login'); // 'login', 'signup', 'forgot'
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const handleLogin = async (credentials) => {
    setIsLoading(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('auth_token', data.access_token);
        localStorage.setItem('user_data', JSON.stringify(data.user));
        setMessage({ type: 'success', text: 'Login successful! Redirecting...' });
        
        setTimeout(() => {
          onLogin({
            success: true,
            user: data.user,
            token: data.access_token
          });
        }, 1000);
      } else {
        setMessage({ 
          type: 'error', 
          text: data.detail || 'Login failed. Please check your credentials.' 
        });
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'Network error. Please check your connection and try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (userData) => {
    setIsLoading(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: 'Account created successfully! Please log in with your credentials.' 
        });
        setTimeout(() => {
          setActiveForm('login');
        }, 2000);
      } else {
        setMessage({ 
          type: 'error', 
          text: data.detail || 'Registration failed. Please try again.' 
        });
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'Network error. Please check your connection and try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (email) => {
    setIsLoading(true);
    setMessage({ type: '', text: '' });

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ 
          type: 'success', 
          text: 'Password reset instructions have been sent to your email.' 
        });
      } else {
        setMessage({ 
          type: 'error', 
          text: data.detail || 'Failed to send reset email. Please try again.' 
        });
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: 'Network error. Please check your connection and try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const clearMessage = () => {
    setMessage({ type: '', text: '' });
  };

  return (
    <div className="auth-container">
      <div className="auth-background">
        <div className="auth-background-overlay"></div>
        <div className="auth-background-pattern"></div>
      </div>
      
      <div className="auth-content">
        <div className="auth-card">
          <div className="auth-header">
            <div className="auth-logo">
              <div className="logo-icon">📈</div>
              <h1>Niveshtor AI</h1>
            </div>
            <p className="auth-subtitle">
              {activeForm === 'login' && 'Welcome back! Sign in to your account'}
              {activeForm === 'signup' && 'Create your account to get started'}
              {activeForm === 'forgot' && 'Reset your password'}
            </p>
          </div>

          {message.text && (
            <div className={`auth-message ${message.type}`}>
              <div className="message-content">
                <span className="message-icon">
                  {message.type === 'success' ? '✓' : '⚠'}
                </span>
                <span className="message-text">{message.text}</span>
                <button 
                  className="message-close"
                  onClick={clearMessage}
                  aria-label="Close message"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          <div className="auth-form-container">
            {activeForm === 'login' && (
              <LoginForm 
                onSubmit={handleLogin}
                isLoading={isLoading}
                onSwitchToSignup={() => setActiveForm('signup')}
                onSwitchToForgot={() => setActiveForm('forgot')}
              />
            )}
            
            {activeForm === 'signup' && (
              <SignupForm 
                onSubmit={handleSignup}
                isLoading={isLoading}
                onSwitchToLogin={() => setActiveForm('login')}
              />
            )}
            
            {activeForm === 'forgot' && (
              <ForgotPasswordForm 
                onSubmit={handleForgotPassword}
                isLoading={isLoading}
                onSwitchToLogin={() => setActiveForm('login')}
              />
            )}
          </div>

          <div className="auth-footer">
            <div className="auth-links">
              {activeForm !== 'login' && (
                <button 
                  className="auth-link"
                  onClick={() => setActiveForm('login')}
                >
                  ← Back to Login
                </button>
              )}
            </div>
            
            <div className="auth-demo">
              <p className="demo-text">Demo Account:</p>
              <p className="demo-credentials">
                <strong>Username:</strong> demo | <strong>Password:</strong> demo123
              </p>
            </div>
          </div>
        </div>

        <div className="auth-features">
          <h3>Why Choose Niveshtor AI?</h3>
          <div className="features-list">
            <div className="feature-item">
              <div className="feature-icon">🤖</div>
              <div className="feature-content">
                <h4>AI-Powered Strategies</h4>
                <p>Advanced algorithms for optimal trading decisions</p>
              </div>
            </div>
            <div className="feature-item">
              <div className="feature-icon">📊</div>
              <div className="feature-content">
                <h4>Real-Time Analytics</h4>
                <p>Live market data and performance tracking</p>
              </div>
            </div>
            <div className="feature-item">
              <div className="feature-icon">🔒</div>
              <div className="feature-content">
                <h4>Secure & Reliable</h4>
                <p>Bank-grade security for your investments</p>
              </div>
            </div>
            <div className="feature-item">
              <div className="feature-icon">📱</div>
              <div className="feature-content">
                <h4>Mobile Ready</h4>
                <p>Trade anywhere with our responsive platform</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthContainer;
