/* Strategy Locked/Coming Soon Styles */
.strategy-locked {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  min-height: 600px;
}

.locked-content {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.lock-icon {
  font-size: 4rem;
  text-align: center;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.locked-content h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.5rem;
  text-align: center;
}

.locked-content > p {
  color: #6b7280;
  text-align: center;
  margin: 0 0 2rem;
  font-size: 1.1rem;
}

.feature-preview {
  margin-bottom: 2rem;
}

.feature-preview h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 1rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: 0.5rem 0;
  color: #374151;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upgrade-section {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.upgrade-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 1rem;
}

.pricing-highlight {
  margin-bottom: 1.5rem;
}

.pricing-highlight .price {
  font-size: 2.5rem;
  font-weight: 800;
  color: #3b82f6;
  display: block;
}

.pricing-highlight .price span {
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
}

.savings {
  font-size: 0.875rem;
  color: #10b981;
  font-weight: 500;
  margin-top: 0.25rem;
}

.upgrade-btn {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.upgrade-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.upgrade-btn.enterprise {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.upgrade-btn.enterprise:hover {
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.upgrade-benefits {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.upgrade-benefits p {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

/* Preview Dashboard */
.preview-dashboard {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  position: relative;
}

.preview-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8fafc;
}

.preview-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.preview-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  background: #3b82f6;
}

.preview-badge.enterprise {
  background: #8b5cf6;
}

.preview-content {
  padding: 1.5rem;
}

.preview-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.preview-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.metric-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
}

.preview-signals {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.preview-signal {
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f8fafc;
}

.signal-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.signal-symbol {
  font-weight: 700;
  color: #1a1a1a;
}

.signal-action {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
}

.signal-action.buy {
  background: #dcfce7;
  color: #166534;
}

.signal-action.sell {
  background: #fee2e2;
  color: #991b1b;
}

.signal-details {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.preview-chart {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-status {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

.chart-placeholder {
  height: 120px;
  position: relative;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 4px;
  overflow: hidden;
}

.chart-lines {
  position: absolute;
  top: 20%;
  left: 10%;
  right: 10%;
  bottom: 20%;
}

.chart-line {
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  margin: 10px 0;
  border-radius: 1px;
  opacity: 0.7;
}

.breakout-points {
  position: absolute;
  top: 30%;
  right: 20%;
}

.breakout-point {
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  margin: 5px 0;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.overlay-content {
  text-align: center;
}

.overlay-icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  opacity: 0.7;
}

.overlay-content p {
  font-weight: 600;
  color: #374151;
  margin: 0;
}

/* Coming Soon Notice */
.coming-soon-notice {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 3rem;
  text-align: center;
  margin-bottom: 2rem;
}

.notice-content h2 {
  font-size: 2rem;
  margin: 0 0 1rem;
  color: #1a1a1a;
}

.notice-content p {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0 0 1.5rem;
}

.eta {
  font-size: 1rem;
  color: #3b82f6;
  font-weight: 600;
}

.strategy-placeholder {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 2rem;
}

.placeholder-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.placeholder-card {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
}

.placeholder-card h3 {
  margin: 0 0 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: #6b7280;
}

.placeholder-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .strategy-locked {
    grid-template-columns: 1fr;
  }
  
  .preview-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .placeholder-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .locked-content {
    padding: 1.5rem;
  }
  
  .locked-content h1 {
    font-size: 1.5rem;
  }
  
  .preview-metrics {
    grid-template-columns: 1fr;
  }
  
  .placeholder-metrics {
    grid-template-columns: 1fr;
  }
  
  .signal-details {
    flex-direction: column;
    gap: 0.25rem;
  }
}
