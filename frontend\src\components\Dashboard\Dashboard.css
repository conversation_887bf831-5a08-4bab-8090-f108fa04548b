/* Dashboard Layout */
.dashboard {
  display: flex;
  height: 100vh;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 280px;
  transition: margin-left 0.3s ease;
}

.dashboard-main.sidebar-collapsed {
  margin-left: 80px;
}

.dashboard-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.page-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 0.5rem;
}

.page-header p {
  color: #6b7280;
  margin: 0;
  font-size: 1rem;
}

/* Connection Banner */
.connection-banner {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  color: white;
  padding: 1.5rem 2rem;
  margin-bottom: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.banner-icon {
  font-size: 2rem;
}

.banner-text {
  flex: 1;
}

.banner-text h3 {
  margin: 0 0 0.25rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.banner-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.banner-action {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.banner-action:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-main {
    margin-left: 0;
  }
  
  .dashboard-main.sidebar-collapsed {
    margin-left: 0;
  }
  
  .page-container {
    padding: 1rem;
  }
  
  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .page-header h1 {
    font-size: 1.5rem;
  }
  
  .connection-banner {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .banner-content {
    gap: 0.75rem;
  }
  
  .banner-text h3 {
    font-size: 1.1rem;
  }
  
  .banner-text p {
    font-size: 0.85rem;
  }
}
