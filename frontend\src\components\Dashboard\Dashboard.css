/* Modern Dashboard Layout */
.dashboard {
  display: flex;
  height: 100vh;
  background: var(--secondary-50);
  font-family: var(--font-family-sans);
  overflow: hidden;
}

.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-base);
  min-width: 0;
}

.dashboard-main.sidebar-collapsed {
  margin-left: var(--sidebar-collapsed-width);
}

.dashboard-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--secondary-50);
  position: relative;
}

.page-container {
  padding: var(--space-8);
  max-width: 1600px;
  margin: 0 auto;
  min-height: calc(100vh - var(--header-height));
}

.page-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--secondary-200);
}

.page-header h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  margin: 0 0 var(--space-2);
  line-height: var(--leading-tight);
}

.page-header p {
  color: var(--secondary-600);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  line-height: var(--leading-relaxed);
}

/* Modern Connection Banner */
.connection-banner {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  padding: var(--space-6) var(--space-8);
  margin-bottom: var(--space-8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--primary-500);
  position: relative;
  overflow: hidden;
}

.connection-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  pointer-events: none;
}

.banner-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  max-width: 1600px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.banner-icon {
  font-size: var(--text-3xl);
  opacity: 0.9;
}

.banner-text {
  flex: 1;
}

.banner-text h3 {
  margin: 0 0 var(--space-1);
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
}

.banner-text p {
  margin: 0;
  opacity: 0.9;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

.banner-action {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
}

.banner-action:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Loading States */
.dashboard-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--secondary-50);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: dashboard-spin 1s linear infinite;
}

@keyframes dashboard-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--secondary-50);
  text-align: center;
  padding: var(--space-8);
}

.error-icon {
  font-size: var(--text-5xl);
  color: var(--error-500);
  margin-bottom: var(--space-4);
}

.error-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  margin: 0 0 var(--space-2);
}

.error-message {
  font-size: var(--text-lg);
  color: var(--secondary-600);
  margin: 0 0 var(--space-6);
  max-width: 500px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-main {
    margin-left: 0;
  }

  .dashboard-main.sidebar-collapsed {
    margin-left: 0;
  }

  .page-container {
    padding: var(--space-4);
  }

  .page-header {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
  }

  .banner-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-4);
  }

  .banner-action {
    align-self: stretch;
    text-align: center;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: var(--space-3);
  }

  .page-header h1 {
    font-size: var(--text-2xl);
  }

  .page-header p {
    font-size: var(--text-base);
  }

  .connection-banner {
    padding: var(--space-4);
    margin-bottom: var(--space-6);
    border-radius: var(--radius-xl);
  }

  .banner-content {
    gap: var(--space-3);
  }

  .banner-text h3 {
    font-size: var(--text-lg);
  }

  .banner-text p {
    font-size: var(--text-sm);
  }

  .banner-action {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
  }
}

@media (max-width: 640px) {
  .page-container {
    padding: var(--space-2);
  }

  .page-header {
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
  }

  .page-header h1 {
    font-size: var(--text-xl);
  }

  .connection-banner {
    padding: var(--space-3);
    margin-bottom: var(--space-4);
  }

  .banner-icon {
    font-size: var(--text-2xl);
  }
}
