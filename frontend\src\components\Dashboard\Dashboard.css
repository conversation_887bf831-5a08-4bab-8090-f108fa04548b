/* Modern Dashboard Layout - Full Page Optimized */
.dashboard {
  display: flex;
  height: 100vh;
  background: var(--secondary-50);
  font-family: var(--font-family-sans);
  overflow: hidden;
}

.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-base);
  min-width: 0;
  height: 100vh;
}

.dashboard-main.sidebar-collapsed {
  margin-left: var(--sidebar-collapsed-width);
}

.dashboard-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--secondary-50);
  position: relative;
  height: calc(100vh - var(--header-height));
}

/* Hide scrollbar but keep functionality */
.dashboard-content::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.dashboard-content::-webkit-scrollbar-thumb {
  background: transparent;
}

.dashboard-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.page-container {
  padding: var(--space-6) var(--space-8);
  width: 100%;
  min-height: calc(100vh - var(--header-height));
  box-sizing: border-box;
}

.page-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--secondary-200);
}

.page-header h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  margin: 0 0 var(--space-2);
  line-height: var(--leading-tight);
}

.page-header p {
  color: var(--secondary-600);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  line-height: var(--leading-relaxed);
}



/* Loading States */
.dashboard-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--secondary-50);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: dashboard-spin 1s linear infinite;
}

@keyframes dashboard-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--secondary-50);
  text-align: center;
  padding: var(--space-8);
}

.error-icon {
  font-size: var(--text-5xl);
  color: var(--error-500);
  margin-bottom: var(--space-4);
}

.error-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  margin: 0 0 var(--space-2);
}

.error-message {
  font-size: var(--text-lg);
  color: var(--secondary-600);
  margin: 0 0 var(--space-6);
  max-width: 500px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-main {
    margin-left: 0;
  }

  .dashboard-main.sidebar-collapsed {
    margin-left: 0;
  }

  .page-container {
    padding: var(--space-4);
  }

  .page-header {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
  }


}

@media (max-width: 768px) {
  .page-container {
    padding: var(--space-3);
  }

  .page-header h1 {
    font-size: var(--text-2xl);
  }

  .page-header p {
    font-size: var(--text-base);
  }


}

@media (max-width: 640px) {
  .page-container {
    padding: var(--space-2);
  }

  .page-header {
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
  }

  .page-header h1 {
    font-size: var(--text-xl);
  }


}
