/* Modern Dashboard Layout */
.dashboard {
  display: flex;
  height: 100vh;
  background: var(--secondary-50);
  font-family: var(--font-family-sans);
  overflow: hidden;
}

.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-base);
  min-width: 0;
}

.dashboard-main.sidebar-collapsed {
  margin-left: var(--sidebar-collapsed-width);
}

.dashboard-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--secondary-50);
  position: relative;
}

/* Professional Page Container */
.page-container {
  padding: var(--space-8);
  max-width: 1800px;
  margin: 0 auto;
  min-height: calc(100vh - var(--header-height));
  position: relative;
}

.page-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.page-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--secondary-200);
}

.page-header h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  margin: 0 0 var(--space-2);
  line-height: var(--leading-tight);
}

.page-header p {
  color: var(--secondary-600);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  line-height: var(--leading-relaxed);
}



/* Loading States */
.dashboard-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--secondary-50);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: dashboard-spin 1s linear infinite;
}

@keyframes dashboard-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--secondary-50);
  text-align: center;
  padding: var(--space-8);
}

.error-icon {
  font-size: var(--text-5xl);
  color: var(--error-500);
  margin-bottom: var(--space-4);
}

.error-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
  margin: 0 0 var(--space-2);
}

.error-message {
  font-size: var(--text-lg);
  color: var(--secondary-600);
  margin: 0 0 var(--space-6);
  max-width: 500px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-main {
    margin-left: 0;
  }

  .dashboard-main.sidebar-collapsed {
    margin-left: 0;
  }

  .page-container {
    padding: var(--space-4);
    max-width: 1200px;
  }


}

@media (max-width: 768px) {
  .page-container {
    padding: var(--space-3);
    max-width: 100%;
  }
}

@media (max-width: 640px) {
  .page-container {
    padding: var(--space-2);
  }
}
