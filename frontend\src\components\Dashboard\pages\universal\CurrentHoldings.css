/* Current Holdings Styles */
.current-holdings {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Holdings Header */
.holdings-header {
  background: linear-gradient(135deg, var(--success-50), var(--primary-50));
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--success-200);
}

.holdings-header h3 {
  margin: 0 0 var(--space-2);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
}

.holdings-header p {
  margin: 0 0 var(--space-4);
  color: var(--secondary-600);
  font-size: var(--text-base);
}

.holdings-summary {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

/* Holdings List */
.holdings-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.holding-card {
  transition: all var(--transition-base);
}

.holding-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Holding Summary */
.holding-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
}

.holding-summary:hover {
  background: var(--secondary-50);
}

.holding-info {
  flex: 1;
}

.stock-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.stock-header h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
}

.holding-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-4);
}

.metric {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.metric-label {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  font-weight: var(--font-medium);
}

.metric-value {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
}

.metric-value.positive {
  color: var(--success-600);
}

.metric-value.negative {
  color: var(--error-600);
}

.expand-icon {
  font-size: var(--text-lg);
  color: var(--secondary-600);
  transition: transform var(--transition-base);
}

/* Holding Details */
.holding-details {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--secondary-200);
}

.details-table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
  background: white;
}

.holding-details-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1800px; /* Ensure all columns are visible */
}

.holding-details-table thead {
  background: var(--secondary-50);
}

.holding-details-table th {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  border-bottom: 2px solid var(--secondary-200);
  font-size: var(--text-xs);
  white-space: nowrap;
}

.holding-details-table td {
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--secondary-100);
  font-size: var(--text-xs);
  color: var(--secondary-700);
  white-space: nowrap;
}

.holding-details-table tbody tr {
  transition: all var(--transition-base);
}

.holding-details-table tbody tr:hover {
  background: var(--secondary-25);
}

/* Table Cell Styles */
.stock-name-cell {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
}

.price-cell {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  text-align: right;
}

.target-price-cell {
  font-weight: var(--font-bold);
  color: var(--success-600);
  text-align: right;
}

.quantity-cell {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  text-align: center;
}

.amount-cell {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  text-align: right;
}

.days-cell {
  font-weight: var(--font-medium);
  color: var(--secondary-700);
  text-align: center;
}

.profit-cell {
  font-weight: var(--font-bold);
  text-align: right;
}

.profit-cell.positive {
  color: var(--success-600);
}

.profit-cell.negative {
  color: var(--error-600);
}

.percent-cell {
  font-weight: var(--font-bold);
  text-align: right;
}

.percent-cell.positive {
  color: var(--success-600);
}

.percent-cell.negative {
  color: var(--error-600);
}

.notional-cell {
  font-weight: var(--font-bold);
  text-align: right;
}

.notional-cell.positive {
  color: var(--success-600);
}

.notional-cell.negative {
  color: var(--error-600);
}

.boolean-cell {
  text-align: center;
}

/* Loading and Empty States */
.holdings-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--success-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-holdings {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
}

.empty-icon {
  font-size: var(--text-4xl);
}

.empty-state h4 {
  margin: 0;
  color: var(--secondary-700);
  font-size: var(--text-lg);
}

.empty-state p {
  margin: 0;
  color: var(--secondary-600);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .holding-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .holdings-summary {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .holdings-header {
    padding: var(--space-4);
    text-align: center;
  }
  
  .holding-summary {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }
  
  .stock-header {
    justify-content: center;
  }
  
  .holding-metrics {
    grid-template-columns: 1fr;
  }
  
  .expand-icon {
    align-self: center;
  }
}

@media (max-width: 640px) {
  .holdings-header h3 {
    font-size: var(--text-lg);
  }
  
  .holding-details-table {
    min-width: 1200px;
  }
  
  .holding-details-table th,
  .holding-details-table td {
    padding: var(--space-2) var(--space-3);
  }
}
