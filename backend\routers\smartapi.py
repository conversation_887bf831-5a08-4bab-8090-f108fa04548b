from fastapi import <PERSON><PERSON>out<PERSON>, HTTP<PERSON>xception, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from sqlalchemy.orm import Session
from database.database import get_db
from services.smartapi_service import smartapi_manager
from services.credential_service import credential_service
from typing import Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()

class SmartAPICredentials(BaseModel):
    api_key: str
    client_id: str
    password: str
    mpin: str
    totp: str

class ConnectionResponse(BaseModel):
    success: bool
    message: str
    user_name: Optional[str] = None
    session_expiry: Optional[str] = None
    error: Optional[str] = None

class StatusResponse(BaseModel):
    is_connected: bool
    user_info: Optional[dict] = None
    error: Optional[str] = None

def get_current_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Extract user ID from JWT token - simplified for demo"""
    # In a real implementation, you'd decode and validate the JWT token
    return "user_123"  # Mock user ID

@router.post("/connect", response_model=ConnectionResponse)
async def connect_smartapi(
    credentials: SmartAPICredentials,
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Connect to SmartAPI with user credentials"""
    try:
        # Save encrypted credentials to database
        save_result = credential_service.save_credentials(
            db, user_id, credentials.dict()
        )
        
        if not save_result["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=save_result["error"]
            )
        
        # Get SmartAPI service for user
        smartapi_service = smartapi_manager.get_service(user_id)
        
        # Set credentials and attempt login
        smartapi_service.set_credentials(
            credentials.api_key,
            credentials.client_id,
            credentials.password,
            credentials.mpin,
            credentials.totp
        )
        
        # Attempt login
        login_result = await smartapi_service.login()
        
        if login_result["success"]:
            # Update connection status in database
            credential_service.update_connection_status(
                db, user_id, True
            )
            
            # Save session tokens
            session_data = {
                'auth_token': smartapi_service.auth_token,
                'feed_token': smartapi_service.feed_token,
                'refresh_token': smartapi_service.refresh_token,
                'session_expiry': smartapi_service.session_expiry,
                'user_name': login_result.get('user_name'),
                'broker': 'Angel One'
            }
            
            credential_service.save_session(db, user_id, session_data)
            
            return ConnectionResponse(
                success=True,
                message=login_result["message"],
                user_name=login_result.get("user_name"),
                session_expiry=login_result.get("session_expiry")
            )
        else:
            # Update connection status with error
            credential_service.update_connection_status(
                db, user_id, False, login_result["error"]
            )
            
            return ConnectionResponse(
                success=False,
                message="Connection failed",
                error=login_result["error"]
            )
            
    except Exception as e:
        logger.error(f"SmartAPI connection error for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Connection failed"
        )

@router.get("/status", response_model=StatusResponse)
async def get_connection_status(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Get current SmartAPI connection status"""
    try:
        # Check if user has active session
        active_session = credential_service.get_active_session(db, user_id)
        
        if active_session:
            # Get SmartAPI service and restore session
            smartapi_service = smartapi_manager.get_service(user_id)
            
            # Restore session tokens
            smartapi_service.auth_token = active_session.get('auth_token')
            smartapi_service.feed_token = active_session.get('feed_token')
            smartapi_service.refresh_token = active_session.get('refresh_token')
            smartapi_service.session_expiry = active_session.get('session_expiry')
            smartapi_service.is_connected = True
            
            # Check if session is still valid
            if smartapi_service.check_session():
                return StatusResponse(
                    is_connected=True,
                    user_info={
                        'name': active_session.get('user_name'),
                        'session_expiry': active_session.get('session_expiry').isoformat() if active_session.get('session_expiry') else None
                    }
                )
        
        return StatusResponse(is_connected=False)
        
    except Exception as e:
        logger.error(f"Error getting connection status for user {user_id}: {e}")
        return StatusResponse(
            is_connected=False,
            error="Failed to check connection status"
        )

@router.post("/disconnect")
async def disconnect_smartapi(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Disconnect from SmartAPI"""
    try:
        # Get SmartAPI service
        smartapi_service = smartapi_manager.get_service(user_id)
        
        # Logout from SmartAPI
        await smartapi_service.logout()
        
        # Update connection status in database
        credential_service.update_connection_status(db, user_id, False)
        
        # Remove service from manager
        smartapi_manager.remove_service(user_id)
        
        return {"success": True, "message": "Disconnected successfully"}
        
    except Exception as e:
        logger.error(f"Error disconnecting user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Disconnect failed"
        )

@router.delete("/credentials")
async def delete_credentials(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Delete stored SmartAPI credentials"""
    try:
        # Disconnect first
        smartapi_service = smartapi_manager.get_service(user_id)
        await smartapi_service.logout()
        smartapi_manager.remove_service(user_id)
        
        # Delete credentials from database
        success = credential_service.delete_credentials(db, user_id)
        
        if success:
            return {"success": True, "message": "Credentials deleted successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete credentials"
            )
            
    except Exception as e:
        logger.error(f"Error deleting credentials for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete credentials"
        )

@router.post("/refresh-session")
async def refresh_session(
    db: Session = Depends(get_db),
    user_id: str = Depends(get_current_user_id)
):
    """Refresh SmartAPI session"""
    try:
        smartapi_service = smartapi_manager.get_service(user_id)
        
        # Get stored credentials
        credentials = credential_service.get_credentials(db, user_id)
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No credentials found"
            )
        
        # Set credentials and refresh session
        smartapi_service.set_credentials(
            credentials['api_key'],
            credentials['client_id'],
            credentials['password'],
            credentials['mpin'],
            credentials['totp']
        )
        
        refresh_result = await smartapi_service.refresh_session()
        
        if refresh_result["success"]:
            # Update session in database
            session_data = {
                'auth_token': smartapi_service.auth_token,
                'feed_token': smartapi_service.feed_token,
                'refresh_token': smartapi_service.refresh_token,
                'session_expiry': smartapi_service.session_expiry
            }
            
            credential_service.save_session(db, user_id, session_data)
            
            return {"success": True, "message": "Session refreshed successfully"}
        else:
            return {"success": False, "error": refresh_result["error"]}
            
    except Exception as e:
        logger.error(f"Error refreshing session for user {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Session refresh failed"
        )
