import React, { useState, useEffect } from 'react'
import LandingPage from './components/LandingPage'
import Dashboard from './components/Dashboard/Dashboard'
import AuthContainer from './components/Auth/AuthContainer'
import './App.css'

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loading, setLoading] = useState(true)
  const [showLanding, setShowLanding] = useState(true)
  const [user, setUser] = useState(null)

  useEffect(() => {
    // Check if user is authenticated
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('auth_token')
      if (token) {
        // Verify token with backend
        const response = await fetch('/api/auth/profile', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        
        if (response.ok) {
          setIsAuthenticated(true)
        } else {
          localStorage.removeItem('auth_token')
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAuthSuccess = (authData) => {
    if (authData.success) {
      setUser(authData.user)
      setIsAuthenticated(true)
      setShowLanding(false)
    }
  }

  const handleSignup = () => {
    // For demo, redirect to login
    setShowLanding(false)
  }

  const handleLogout = () => {
    localStorage.removeItem('auth_token')
    setIsAuthenticated(false)
    setUser(null)
    setShowLanding(true)
  }

  if (loading) {
    return (
      <div className="app-loading">
        <div className="loading-spinner"></div>
        <p>Loading Niveshtor AI...</p>
      </div>
    )
  }

  // Show landing page for non-authenticated users
  if (showLanding && !isAuthenticated) {
    return (
      <LandingPage
        onLogin={() => setShowLanding(false)}
        onSignup={handleSignup}
      />
    )
  }

  // Show login form
  if (!isAuthenticated) {
    return <AuthContainer onLogin={handleAuthSuccess} />
  }

  // Show main dashboard
  return (
    <div className="App">
      <Dashboard
        user={user}
        onLogout={handleLogout}
      />
    </div>
  )
}



export default App
