@echo off
echo Starting Niveshtor AI Trading Platform...
echo.

echo Starting Backend (FastAPI)...
start "Backend" cmd /k "cd backend && python main.py"

echo Waiting for backend to start...
timeout /t 3 /nobreak > nul

echo Starting Frontend (React + Vite)...
start "Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo ========================================
echo Niveshtor AI Trading Platform Started!
echo ========================================
echo Backend API: http://localhost:8000
echo Frontend App: http://localhost:5173
echo API Documentation: http://localhost:8000/docs
echo.
echo Press any key to stop all services...
pause > nul

echo Stopping services...
taskkill /f /im python.exe 2>nul
taskkill /f /im node.exe 2>nul
echo Services stopped.
