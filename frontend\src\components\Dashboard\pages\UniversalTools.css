/* Universal Tools Page */
.universal-tools {
  padding: 0;
}

/* Tools Tabs */
.tools-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  background: white;
  border-radius: 12px;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
}

.tab-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.tab-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.tab-btn.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

/* Tab Content */
.tab-content {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  padding: 2rem;
}

.tool-header {
  margin-bottom: 2rem;
}

.tool-header h2 {
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
}

.tool-header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

/* Capital Management */
.capital-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.capital-settings {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.capital-settings h3 {
  margin: 0 0 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.setting-group {
  margin-bottom: 1rem;
}

.setting-group label {
  display: block;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.setting-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.3s ease;
}

.setting-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.capital-calculator {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.capital-calculator h3 {
  margin: 0 0 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.calculator-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.metric-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.metric-value {
  font-size: 1rem;
  font-weight: 700;
  color: #1a1a1a;
}

/* Stock Trading */
.trading-interface {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.stock-search {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.stock-search h3 {
  margin: 0 0 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-results {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
}

.search-result {
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-result:hover {
  background: #f8fafc;
}

.search-result:last-child {
  border-bottom: none;
}

.stock-info {
  display: flex;
  flex-direction: column;
}

.stock-symbol {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.stock-name {
  font-size: 0.75rem;
  color: #6b7280;
}

.stock-price {
  font-weight: 600;
  color: #374151;
}

.order-form {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.order-form h3 {
  margin: 0 0 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.stock-details {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.order-inputs {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.order-select, .order-input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.suggested-quantity {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.suggestion {
  font-weight: 600;
  color: #1e40af;
}

.place-order-btn {
  width: 100%;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.place-order-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.place-order-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* BOH Filter */
.filter-interface {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
}

.filter-controls {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
  height: fit-content;
}

.filter-controls h3 {
  margin: 0 0 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group label {
  display: block;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.filter-input, .filter-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.filter-input.small {
  width: 80px;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filtered-results {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.filtered-results h3 {
  margin: 0 0 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
}

.stocks-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 500px;
  overflow-y: auto;
}

.stock-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.stock-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.stock-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.stock-identity {
  display: flex;
  flex-direction: column;
}

.symbol {
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.875rem;
}

.name {
  font-size: 0.75rem;
  color: #6b7280;
}

.stock-metrics {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.price {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.volume {
  font-size: 0.75rem;
  color: #6b7280;
}

.stock-tags {
  display: flex;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.tag.market-cap {
  background: #dbeafe;
  color: #1e40af;
}

.tag.sector {
  background: #dcfce7;
  color: #166534;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .capital-grid,
  .trading-interface,
  .filter-interface {
    grid-template-columns: 1fr;
  }
  
  .tools-tabs {
    flex-direction: column;
  }
  
  .tab-btn {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .tab-content {
    padding: 1.5rem;
  }
  
  .tool-header h2 {
    font-size: 1.25rem;
  }
  
  .calculator-metrics {
    gap: 0.75rem;
  }
  
  .metric-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .stock-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .stock-metrics {
    align-items: flex-start;
  }
}
