import React from 'react';
import './Card.css';

const Card = ({
  children,
  variant = 'default',
  padding = 'default',
  shadow = 'default',
  hover = false,
  className = '',
  onClick,
  ...props
}) => {
  const baseClasses = 'card';
  const variantClasses = `card--${variant}`;
  const paddingClasses = `card--padding-${padding}`;
  const shadowClasses = `card--shadow-${shadow}`;
  const interactiveClasses = [
    hover && 'card--hover',
    onClick && 'card--clickable'
  ].filter(Boolean).join(' ');

  const cardClasses = [
    baseClasses,
    variantClasses,
    paddingClasses,
    shadowClasses,
    interactiveClasses,
    className
  ].filter(Boolean).join(' ');

  const Element = onClick ? 'button' : 'div';

  return (
    <Element
      className={cardClasses}
      onClick={onClick}
      {...props}
    >
      {children}
    </Element>
  );
};

const CardHeader = ({ children, className = '', ...props }) => (
  <div className={`card__header ${className}`} {...props}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', ...props }) => (
  <h3 className={`card__title ${className}`} {...props}>
    {children}
  </h3>
);

const CardSubtitle = ({ children, className = '', ...props }) => (
  <p className={`card__subtitle ${className}`} {...props}>
    {children}
  </p>
);

const CardContent = ({ children, className = '', ...props }) => (
  <div className={`card__content ${className}`} {...props}>
    {children}
  </div>
);

const CardFooter = ({ children, className = '', ...props }) => (
  <div className={`card__footer ${className}`} {...props}>
    {children}
  </div>
);

const CardActions = ({ children, className = '', align = 'right', ...props }) => (
  <div className={`card__actions card__actions--${align} ${className}`} {...props}>
    {children}
  </div>
);

// Compound component exports
Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Subtitle = CardSubtitle;
Card.Content = CardContent;
Card.Footer = CardFooter;
Card.Actions = CardActions;

export default Card;
