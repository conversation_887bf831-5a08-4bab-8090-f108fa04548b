/* System Status Page */
.system-status-page {
  padding: 0;
  background: var(--secondary-50);
}

.status-alert {
  margin-bottom: var(--space-8);
}

/* System Health Card */
.system-health-card {
  margin-bottom: var(--space-8);
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.health-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--secondary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
  transition: all var(--transition-base);
}

.health-item:hover {
  background: var(--secondary-100);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.health-icon {
  font-size: var(--text-2xl);
}

.health-info {
  flex: 1;
}

.health-name {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  margin-bottom: var(--space-1);
  text-transform: capitalize;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.stat-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--success-500));
  opacity: 0;
  transition: opacity var(--transition-base);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  font-size: var(--text-4xl);
  margin-bottom: var(--space-4);
  opacity: 0.8;
}

.stat-value {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--primary-600);
  margin: 0 0 var(--space-2);
  line-height: var(--leading-none);
}

/* Features Card */
.features-card {
  margin-bottom: var(--space-8);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--secondary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
  transition: all var(--transition-base);
}

.feature-item:hover {
  background: var(--secondary-100);
  border-color: var(--primary-300);
  transform: translateX(4px);
}

.feature-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  flex-shrink: 0;
}

.feature-icon {
  font-size: var(--text-xl);
}

.feature-info {
  flex: 1;
}

.feature-name {
  margin: 0 0 var(--space-1);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  line-height: var(--leading-tight);
}

.feature-description {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}

/* Actions Card */
.actions-card {
  margin-bottom: var(--space-8);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .health-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-3);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-4);
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
  }
  
  .feature-status {
    flex-direction: row;
  }
}

@media (max-width: 768px) {
  .health-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .health-item {
    padding: var(--space-3);
  }
  
  .feature-item {
    padding: var(--space-3);
  }
  
  .stat-icon {
    font-size: var(--text-3xl);
  }
  
  .stat-value {
    font-size: var(--text-3xl);
  }
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .health-item {
    flex-direction: column;
    text-align: center;
    gap: var(--space-2);
  }
  
  .feature-name {
    font-size: var(--text-base);
  }
  
  .feature-description {
    font-size: var(--text-xs);
  }
}
