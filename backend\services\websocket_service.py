"""
WebSocket service for real-time market data and order updates
"""

from fastapi import WebSocket, WebSocketDisconnect
from SmartApi.smartWebSocketV2 import SmartWebSocketV2
import json
import asyncio
import logging
from typing import Dict, List, Set
from datetime import datetime

logger = logging.getLogger(__name__)

class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_subscriptions: Dict[str, Set[str]] = {}
        self.smartapi_websockets: Dict[str, SmartWebSocketV2] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """Connect a user's WebSocket"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_subscriptions[user_id] = set()
        logger.info(f"WebSocket connected for user {user_id}")
    
    def disconnect(self, user_id: str):
        """Disconnect a user's WebSocket"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        
        if user_id in self.user_subscriptions:
            del self.user_subscriptions[user_id]
        
        if user_id in self.smartapi_websockets:
            try:
                self.smartapi_websockets[user_id].close_connection()
            except:
                pass
            del self.smartapi_websockets[user_id]
        
        logger.info(f"WebSocket disconnected for user {user_id}")
    
    async def send_personal_message(self, message: dict, user_id: str):
        """Send message to specific user"""
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to user {user_id}: {e}")
                self.disconnect(user_id)
    
    async def broadcast_message(self, message: dict):
        """Broadcast message to all connected users"""
        disconnected_users = []
        
        for user_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to user {user_id}: {e}")
                disconnected_users.append(user_id)
        
        # Clean up disconnected users
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    def setup_smartapi_websocket(self, user_id: str, auth_token: str, feed_token: str, client_id: str):
        """Setup SmartAPI WebSocket for real-time data"""
        try:
            def on_message(ws, message):
                """Handle incoming SmartAPI WebSocket messages"""
                try:
                    data = json.loads(message)
                    
                    # Process different types of messages
                    if 'ltp' in data:
                        # Market data update
                        asyncio.create_task(self.send_personal_message({
                            'type': 'market_data',
                            'data': data,
                            'timestamp': datetime.now().isoformat()
                        }, user_id))
                    
                    elif 'order' in data:
                        # Order update
                        asyncio.create_task(self.send_personal_message({
                            'type': 'order_update',
                            'data': data,
                            'timestamp': datetime.now().isoformat()
                        }, user_id))
                    
                except Exception as e:
                    logger.error(f"Error processing SmartAPI message for user {user_id}: {e}")
            
            def on_error(ws, error):
                logger.error(f"SmartAPI WebSocket error for user {user_id}: {error}")
            
            def on_close(ws):
                logger.info(f"SmartAPI WebSocket closed for user {user_id}")
            
            def on_open(ws):
                logger.info(f"SmartAPI WebSocket opened for user {user_id}")
            
            # Create SmartAPI WebSocket connection
            smartapi_ws = SmartWebSocketV2(
                auth_token=auth_token,
                api_key="your_api_key",  # This should come from user credentials
                client_id=client_id,
                feed_token=feed_token
            )
            
            smartapi_ws.on_open = on_open
            smartapi_ws.on_data = on_message
            smartapi_ws.on_error = on_error
            smartapi_ws.on_close = on_close
            
            # Start the connection
            smartapi_ws.connect()
            
            self.smartapi_websockets[user_id] = smartapi_ws
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting up SmartAPI WebSocket for user {user_id}: {e}")
            return False
    
    async def subscribe_to_symbols(self, user_id: str, symbols: List[str]):
        """Subscribe to real-time data for symbols"""
        try:
            if user_id not in self.smartapi_websockets:
                await self.send_personal_message({
                    'type': 'error',
                    'message': 'SmartAPI WebSocket not connected'
                }, user_id)
                return False
            
            smartapi_ws = self.smartapi_websockets[user_id]
            
            # Subscribe to symbols
            for symbol in symbols:
                # Add symbol to user subscriptions
                self.user_subscriptions[user_id].add(symbol)
                
                # Subscribe via SmartAPI WebSocket
                # Note: Actual subscription format depends on SmartAPI WebSocket implementation
                subscription_data = {
                    "action": "subscribe",
                    "params": {
                        "mode": 1,  # LTP mode
                        "tokenList": [
                            {
                                "exchangeType": 1,  # NSE
                                "tokens": [symbol]  # This should be token, not symbol
                            }
                        ]
                    }
                }
                
                smartapi_ws.send(json.dumps(subscription_data))
            
            await self.send_personal_message({
                'type': 'subscription_success',
                'symbols': symbols,
                'message': f'Subscribed to {len(symbols)} symbols'
            }, user_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error subscribing to symbols for user {user_id}: {e}")
            await self.send_personal_message({
                'type': 'error',
                'message': f'Subscription failed: {str(e)}'
            }, user_id)
            return False
    
    async def unsubscribe_from_symbols(self, user_id: str, symbols: List[str]):
        """Unsubscribe from real-time data for symbols"""
        try:
            if user_id not in self.smartapi_websockets:
                return False
            
            smartapi_ws = self.smartapi_websockets[user_id]
            
            # Unsubscribe from symbols
            for symbol in symbols:
                # Remove symbol from user subscriptions
                self.user_subscriptions[user_id].discard(symbol)
                
                # Unsubscribe via SmartAPI WebSocket
                unsubscription_data = {
                    "action": "unsubscribe",
                    "params": {
                        "mode": 1,
                        "tokenList": [
                            {
                                "exchangeType": 1,
                                "tokens": [symbol]
                            }
                        ]
                    }
                }
                
                smartapi_ws.send(json.dumps(unsubscription_data))
            
            await self.send_personal_message({
                'type': 'unsubscription_success',
                'symbols': symbols,
                'message': f'Unsubscribed from {len(symbols)} symbols'
            }, user_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error unsubscribing from symbols for user {user_id}: {e}")
            return False
    
    async def send_heartbeat(self):
        """Send heartbeat to all connected clients"""
        heartbeat_message = {
            'type': 'heartbeat',
            'timestamp': datetime.now().isoformat()
        }
        await self.broadcast_message(heartbeat_message)

# Global WebSocket manager instance
websocket_manager = WebSocketManager()

# Background task for heartbeat
async def heartbeat_task():
    """Background task to send periodic heartbeats"""
    while True:
        await asyncio.sleep(30)  # Send heartbeat every 30 seconds
        await websocket_manager.send_heartbeat()
