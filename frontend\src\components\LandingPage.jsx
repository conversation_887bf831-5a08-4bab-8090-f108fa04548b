import React, { useState } from 'react';
import './LandingPage.css';

const LandingPage = ({ onLogin, onSignup }) => {
  const [activeTab, setActiveTab] = useState('monthly');

  const features = [
    {
      icon: '📈',
      title: 'Weekly High Strategy',
      description: 'Automated breakout detection with GTT order management for consistent profits'
    },
    {
      icon: '⚡',
      title: 'RSI Strategy',
      description: 'Advanced momentum indicators with precise entry and exit signals'
    },
    {
      icon: '🎯',
      title: 'Consolidated Breakout',
      description: 'Multi-timeframe analysis for high-probability breakout trades'
    },
    {
      icon: '🔄',
      title: 'Real-time Signals',
      description: 'Instant notifications and automated order execution via SmartAPI'
    },
    {
      icon: '📊',
      title: 'Advanced Backtesting',
      description: 'Historical performance analysis with detailed metrics and visualization'
    },
    {
      icon: '🛡️',
      title: 'Risk Management',
      description: 'Intelligent capital allocation and position sizing for optimal returns'
    }
  ];

  const pricingPlans = {
    monthly: [
      {
        name: 'Starter',
        price: '₹2,999',
        period: '/month',
        features: [
          'Weekly High Strategy',
          'Basic Backtesting',
          'Email Alerts',
          'Standard Support',
          'Up to 50 stocks'
        ],
        popular: false
      },
      {
        name: 'Professional',
        price: '₹5,999',
        period: '/month',
        features: [
          'All Starter features',
          'RSI Strategy',
          'Advanced Backtesting',
          'Real-time Alerts',
          'Priority Support',
          'Up to 200 stocks',
          'Custom Parameters'
        ],
        popular: true
      },
      {
        name: 'Enterprise',
        price: '₹12,999',
        period: '/month',
        features: [
          'All Professional features',
          'Consolidated Breakout Strategy',
          'API Access',
          'White-label Solution',
          'Dedicated Support',
          'Unlimited stocks',
          'Custom Strategies'
        ],
        popular: false
      }
    ],
    yearly: [
      {
        name: 'Starter',
        price: '₹29,999',
        period: '/year',
        originalPrice: '₹35,988',
        features: [
          'Weekly High Strategy',
          'Basic Backtesting',
          'Email Alerts',
          'Standard Support',
          'Up to 50 stocks'
        ],
        popular: false
      },
      {
        name: 'Professional',
        price: '₹59,999',
        period: '/year',
        originalPrice: '₹71,988',
        features: [
          'All Starter features',
          'RSI Strategy',
          'Advanced Backtesting',
          'Real-time Alerts',
          'Priority Support',
          'Up to 200 stocks',
          'Custom Parameters'
        ],
        popular: true
      },
      {
        name: 'Enterprise',
        price: '₹1,29,999',
        period: '/year',
        originalPrice: '₹1,55,988',
        features: [
          'All Professional features',
          'Consolidated Breakout Strategy',
          'API Access',
          'White-label Solution',
          'Dedicated Support',
          'Unlimited stocks',
          'Custom Strategies'
        ],
        popular: false
      }
    ]
  };

  return (
    <div className="landing-page">
      {/* Navigation */}
      <nav className="navbar">
        <div className="nav-container">
          <div className="nav-logo">
            <h2>Niveshtor AI</h2>
          </div>
          <div className="nav-actions">
            <button onClick={onLogin} className="btn-secondary">
              Login
            </button>
            <button onClick={onSignup} className="btn-primary">
              Start Free Trial
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero">
        <div className="hero-container">
          <div className="hero-content">
            <h1 className="hero-title">
              Algorithmic Trading
              <span className="gradient-text"> Simplified</span>
            </h1>
            <p className="hero-subtitle">
              Transform your trading with AI-powered strategies. Automated signal generation, 
              intelligent risk management, and seamless broker integration for consistent profits.
            </p>
            <div className="hero-stats">
              <div className="stat">
                <span className="stat-number">85%+</span>
                <span className="stat-label">Success Rate</span>
              </div>
              <div className="stat">
                <span className="stat-number">₹50L+</span>
                <span className="stat-label">Daily Volume</span>
              </div>
              <div className="stat">
                <span className="stat-number">10K+</span>
                <span className="stat-label">Active Traders</span>
              </div>
            </div>
            <div className="hero-actions">
              <button onClick={onSignup} className="btn-primary btn-large">
                Start Free Trial
              </button>
              <button className="btn-secondary btn-large">
                Watch Demo
              </button>
            </div>
          </div>
          <div className="hero-visual">
            <div className="trading-chart">
              <div className="chart-header">
                <span className="chart-title">Live Trading Performance</span>
                <span className="chart-profit">+24.5% Today</span>
              </div>
              <div className="chart-content">
                <div className="chart-line"></div>
                <div className="chart-signals">
                  <div className="signal buy">BUY</div>
                  <div className="signal sell">SELL</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <div className="section-header">
            <h2>Powerful Trading Strategies</h2>
            <p>Advanced algorithms designed for consistent market outperformance</p>
          </div>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-icon">{feature.icon}</div>
                <h3 className="feature-title">{feature.title}</h3>
                <p className="feature-description">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="pricing">
        <div className="container">
          <div className="section-header">
            <h2>Choose Your Trading Plan</h2>
            <p>Flexible pricing for traders at every level</p>
          </div>
          
          <div className="pricing-toggle">
            <button 
              className={`toggle-btn ${activeTab === 'monthly' ? 'active' : ''}`}
              onClick={() => setActiveTab('monthly')}
            >
              Monthly
            </button>
            <button 
              className={`toggle-btn ${activeTab === 'yearly' ? 'active' : ''}`}
              onClick={() => setActiveTab('yearly')}
            >
              Yearly
              <span className="discount-badge">Save 17%</span>
            </button>
          </div>

          <div className="pricing-grid">
            {pricingPlans[activeTab].map((plan, index) => (
              <div key={index} className={`pricing-card ${plan.popular ? 'popular' : ''}`}>
                {plan.popular && <div className="popular-badge">Most Popular</div>}
                <div className="plan-header">
                  <h3 className="plan-name">{plan.name}</h3>
                  <div className="plan-price">
                    <span className="price">{plan.price}</span>
                    <span className="period">{plan.period}</span>
                  </div>
                  {plan.originalPrice && (
                    <div className="original-price">
                      <span>Was {plan.originalPrice}</span>
                    </div>
                  )}
                </div>
                <ul className="plan-features">
                  {plan.features.map((feature, idx) => (
                    <li key={idx}>
                      <span className="checkmark">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <button 
                  onClick={onSignup}
                  className={`plan-button ${plan.popular ? 'btn-primary' : 'btn-secondary'}`}
                >
                  Get Started
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Transform Your Trading?</h2>
            <p>Join thousands of successful traders using Niveshtor AI</p>
            <div className="cta-actions">
              <button onClick={onSignup} className="btn-primary btn-large">
                Start Free Trial
              </button>
              <p className="cta-note">No credit card required • 14-day free trial</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-brand">
              <h3>Niveshtor AI</h3>
              <p>Algorithmic trading platform for the modern investor</p>
            </div>
            <div className="footer-links">
              <div className="link-group">
                <h4>Product</h4>
                <a href="#features">Features</a>
                <a href="#pricing">Pricing</a>
                <a href="#demo">Demo</a>
              </div>
              <div className="link-group">
                <h4>Support</h4>
                <a href="#help">Help Center</a>
                <a href="#contact">Contact</a>
                <a href="#docs">Documentation</a>
              </div>
              <div className="link-group">
                <h4>Legal</h4>
                <a href="#privacy">Privacy Policy</a>
                <a href="#terms">Terms of Service</a>
                <a href="#disclaimer">Risk Disclaimer</a>
              </div>
            </div>
          </div>
          <div className="footer-bottom">
            <p>&copy; 2024 Niveshtor AI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
