from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="Niveshtor AI Trading API",
    description="Advanced Trading Platform with Real-time Market Data",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "Niveshtor AI Trading API",
        "version": "1.0.0",
        "status": "active",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "niveshtor-ai-api"}

# Simple market data endpoint
@app.get("/api/market/quote/{symbol}")
async def get_stock_quote(symbol: str):
    try:
        import yfinance as yf
        ticker = yf.Ticker(symbol)
        info = ticker.info
        
        return {
            "symbol": symbol.upper(),
            "price": info.get("currentPrice", 0),
            "change": info.get("regularMarketChange", 0),
            "changePercent": info.get("regularMarketChangePercent", 0),
            "volume": info.get("volume", 0)
        }
    except Exception as e:
        return {"error": str(e)}

# Simple auth endpoint
@app.post("/api/auth/login")
async def login(credentials: dict):
    if credentials.get("username") == "demo" and credentials.get("password") == "demo123":
        return {
            "access_token": "mock_jwt_token_12345",
            "token_type": "bearer",
            "user_id": "user_123"
        }
    else:
        return {"error": "Invalid credentials"}

# Simple SmartAPI endpoints
@app.post("/api/smartapi/connect")
async def connect_smartapi(credentials: dict):
    # Mock connection for demo
    return {
        "success": True,
        "message": "Connected to Angel One successfully",
        "user_name": "Demo User",
        "session_expiry": "2024-01-16T10:30:00"
    }

@app.get("/api/smartapi/status")
async def smartapi_status():
    # Mock status for demo
    return {
        "is_connected": False,
        "user_info": None,
        "error": None
    }

@app.post("/api/smartapi/disconnect")
async def disconnect_smartapi():
    return {"success": True, "message": "Disconnected successfully"}

if __name__ == "__main__":
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
