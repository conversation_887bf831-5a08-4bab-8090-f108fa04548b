from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
import asyncio
import aiohttp
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/quote/{symbol}")
async def get_stock_quote(symbol: str):
    """Get real-time stock quote"""
    try:
        ticker = yf.Ticker(symbol)
        info = ticker.info
        
        return {
            "symbol": symbol.upper(),
            "price": info.get("currentPrice", 0),
            "change": info.get("regularMarketChange", 0),
            "changePercent": info.get("regularMarketChangePercent", 0),
            "volume": info.get("volume", 0),
            "marketCap": info.get("marketCap", 0),
            "dayHigh": info.get("dayHigh", 0),
            "dayLow": info.get("dayLow", 0),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error fetching quote for {symbol}: {e}")
        raise HTTPException(status_code=400, detail=f"Error fetching data for {symbol}")

@router.get("/history/{symbol}")
async def get_stock_history(
    symbol: str,
    period: str = "1mo",
    interval: str = "1d"
):
    """Get historical stock data"""
    try:
        ticker = yf.Ticker(symbol)
        hist = ticker.history(period=period, interval=interval)
        
        # Convert to list of dictionaries
        data = []
        for date, row in hist.iterrows():
            data.append({
                "date": date.strftime("%Y-%m-%d"),
                "open": float(row["Open"]),
                "high": float(row["High"]),
                "low": float(row["Low"]),
                "close": float(row["Close"]),
                "volume": int(row["Volume"])
            })
        
        return {
            "symbol": symbol.upper(),
            "period": period,
            "interval": interval,
            "data": data
        }
    except Exception as e:
        logger.error(f"Error fetching history for {symbol}: {e}")
        raise HTTPException(status_code=400, detail=f"Error fetching history for {symbol}")

@router.get("/search/{query}")
async def search_stocks(query: str):
    """Search for stocks by name or symbol"""
    try:
        # This is a basic implementation - you might want to use a proper search API
        ticker = yf.Ticker(query)
        info = ticker.info
        
        if info.get("symbol"):
            return [{
                "symbol": info.get("symbol", query.upper()),
                "name": info.get("longName", "Unknown"),
                "sector": info.get("sector", "Unknown"),
                "industry": info.get("industry", "Unknown")
            }]
        else:
            return []
    except Exception as e:
        logger.error(f"Error searching for {query}: {e}")
        return []

@router.get("/market-status")
async def get_market_status():
    """Get current market status"""
    try:
        # Get market hours for major indices
        spy = yf.Ticker("SPY")
        info = spy.info
        
        return {
            "isOpen": True,  # You'd implement proper market hours logic here
            "nextOpen": "09:30:00",
            "nextClose": "16:00:00",
            "timezone": "America/New_York",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Error getting market status: {e}")
        raise HTTPException(status_code=500, detail="Error getting market status")
