import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Badge } from '../../../UI';
import './CurrentHoldings.css';

const CurrentHoldings = () => {
  const [holdings, setHoldings] = useState([]);
  const [expandedHoldings, setExpandedHoldings] = useState(new Set());
  const [loading, setLoading] = useState(true);

  // Mock holdings data - replace with real data
  useEffect(() => {
    const mockHoldings = [
      {
        stockName: 'RELIANCE',
        transactions: [
          {
            buyDate: '2024-01-15',
            buyPrice: 2450.00,
            actualBuyQuantity: 4,
            amount: 9800.00,
            sellPrice: null,
            sellDate: null,
            profitAmount: null,
            profitPercent: null
          },
          {
            buyDate: '2024-01-20',
            buyPrice: 2380.00,
            actualBuyQuantity: 8,
            amount: 19040.00,
            sellPrice: null,
            sellDate: null,
            profitAmount: null,
            profitPercent: null
          }
        ],
        currentPrice: 2505.00,
        totalQuantity: 12,
        totalInvested: 28840.00,
        averagePrice: 2403.33,
        targetPrice: 2547.53, // Average Price × 1.06
        lastWeekHighestPrice: 2520.00,
        ignorableLowerPrice: 2200.00,
        holdingDays: 25
      },
      {
        stockName: 'TCS',
        transactions: [
          {
            buyDate: '2024-01-10',
            buyPrice: 3850.00,
            actualBuyQuantity: 5,
            amount: 19250.00,
            sellPrice: 4100.00,
            sellDate: '2024-01-25',
            profitAmount: 1250.00,
            profitPercent: 6.49
          }
        ],
        currentPrice: 3920.00,
        totalQuantity: 0, // Sold
        totalInvested: 0,
        averagePrice: 3850.00,
        targetPrice: 4081.00,
        lastWeekHighestPrice: 3950.00,
        ignorableLowerPrice: 3600.00,
        holdingDays: 15
      }
    ];

    setHoldings(mockHoldings);
    setLoading(false);
  }, []);

  const toggleHoldingExpansion = (stockName) => {
    const newExpanded = new Set(expandedHoldings);
    if (newExpanded.has(stockName)) {
      newExpanded.delete(stockName);
    } else {
      newExpanded.add(stockName);
    }
    setExpandedHoldings(newExpanded);
  };

  const formatPrice = (price) => {
    return `₹${price?.toFixed(2) || '0.00'}`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const calculateNotionalPL = (holding) => {
    if (holding.totalQuantity === 0) return { amount: 0, percent: 0 };
    
    const currentValue = holding.totalQuantity * holding.currentPrice;
    const investedValue = holding.totalInvested;
    const plAmount = currentValue - investedValue;
    const plPercent = (plAmount / investedValue) * 100;
    
    return { amount: plAmount, percent: plPercent };
  };

  const isOutsideIgnorableRange = (holding) => {
    return holding.lastWeekHighestPrice > holding.ignorableLowerPrice;
  };

  const renderHoldingDetails = (holding) => {
    const notionalPL = calculateNotionalPL(holding);
    
    return (
      <div className="holding-details">
        <div className="details-table-container">
          <table className="holding-details-table">
            <thead>
              <tr>
                <th>Buy Date</th>
                <th>Stock Name</th>
                <th>Buy Price</th>
                <th>Actual Buy Quantity</th>
                <th>Amount</th>
                <th>Total Quantity</th>
                <th>Total Invested</th>
                <th>Average Price</th>
                <th>Target Price</th>
                <th>Sell Price</th>
                <th>Sell Date</th>
                <th>Total Invested Amount on This Date</th>
                <th>Holding Days</th>
                <th>Profit Amount</th>
                <th>Profit %</th>
                <th>Overall Notional P/L on All Units</th>
                <th>Notional P/L %</th>
                <th>Last Week's Highest Price</th>
                <th>Ignorable Lower Price</th>
                <th>Is Last Week's Highest Price Outside Ignorable Range?</th>
              </tr>
            </thead>
            <tbody>
              {holding.transactions.map((transaction, index) => (
                <tr key={index}>
                  <td>{formatDate(transaction.buyDate)}</td>
                  <td className="stock-name-cell">{holding.stockName}</td>
                  <td className="price-cell">{formatPrice(transaction.buyPrice)}</td>
                  <td className="quantity-cell">{transaction.actualBuyQuantity}</td>
                  <td className="amount-cell">{formatPrice(transaction.amount)}</td>
                  <td className="quantity-cell">{holding.totalQuantity}</td>
                  <td className="amount-cell">{formatPrice(holding.totalInvested)}</td>
                  <td className="price-cell">{formatPrice(holding.averagePrice)}</td>
                  <td className="target-price-cell">{formatPrice(holding.targetPrice)}</td>
                  <td className="price-cell">{formatPrice(transaction.sellPrice)}</td>
                  <td>{formatDate(transaction.sellDate)}</td>
                  <td className="amount-cell">{formatPrice(transaction.amount)}</td>
                  <td className="days-cell">{holding.holdingDays}</td>
                  <td className={`profit-cell ${transaction.profitAmount >= 0 ? 'positive' : 'negative'}`}>
                    {transaction.profitAmount ? formatPrice(transaction.profitAmount) : 'N/A'}
                  </td>
                  <td className={`percent-cell ${transaction.profitPercent >= 0 ? 'positive' : 'negative'}`}>
                    {transaction.profitPercent ? `${transaction.profitPercent.toFixed(2)}%` : 'N/A'}
                  </td>
                  <td className={`notional-cell ${notionalPL.amount >= 0 ? 'positive' : 'negative'}`}>
                    {formatPrice(notionalPL.amount)}
                  </td>
                  <td className={`percent-cell ${notionalPL.percent >= 0 ? 'positive' : 'negative'}`}>
                    {notionalPL.percent.toFixed(2)}%
                  </td>
                  <td className="price-cell">{formatPrice(holding.lastWeekHighestPrice)}</td>
                  <td className="price-cell">{formatPrice(holding.ignorableLowerPrice)}</td>
                  <td className="boolean-cell">
                    <Badge variant={isOutsideIgnorableRange(holding) ? 'success' : 'secondary'}>
                      {isOutsideIgnorableRange(holding) ? 'Yes' : 'No'}
                    </Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="holdings-loading">
        <Card variant="elevated">
          <Card.Content>
            <div className="loading-content">
              <div className="loading-spinner"></div>
              <p>Loading current holdings...</p>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="current-holdings">
      <div className="holdings-header">
        <h3>Current Holdings</h3>
        <p>Detailed view of all stock positions with collapsible transaction history</p>
        <div className="holdings-summary">
          <Badge variant="primary">
            Total Holdings: {holdings.filter(h => h.totalQuantity > 0).length}
          </Badge>
          <Badge variant="success">
            Total Invested: ₹{holdings.reduce((sum, h) => sum + h.totalInvested, 0).toLocaleString()}
          </Badge>
        </div>
      </div>

      <div className="holdings-list">
        {holdings.map((holding) => {
          const notionalPL = calculateNotionalPL(holding);
          const isExpanded = expandedHoldings.has(holding.stockName);
          
          return (
            <Card key={holding.stockName} variant="elevated" className="holding-card">
              <Card.Content>
                <div className="holding-summary" onClick={() => toggleHoldingExpansion(holding.stockName)}>
                  <div className="holding-info">
                    <div className="stock-header">
                      <h4>{holding.stockName}</h4>
                      <Badge variant={holding.totalQuantity > 0 ? 'success' : 'secondary'}>
                        {holding.totalQuantity > 0 ? 'Active' : 'Sold'}
                      </Badge>
                    </div>
                    <div className="holding-metrics">
                      <div className="metric">
                        <span className="metric-label">Quantity:</span>
                        <span className="metric-value">{holding.totalQuantity}</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Avg Price:</span>
                        <span className="metric-value">{formatPrice(holding.averagePrice)}</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">Current Price:</span>
                        <span className="metric-value">{formatPrice(holding.currentPrice)}</span>
                      </div>
                      <div className="metric">
                        <span className="metric-label">P/L:</span>
                        <span className={`metric-value ${notionalPL.amount >= 0 ? 'positive' : 'negative'}`}>
                          {formatPrice(notionalPL.amount)} ({notionalPL.percent.toFixed(2)}%)
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="expand-icon">
                    {isExpanded ? '▲' : '▼'}
                  </div>
                </div>
                
                {isExpanded && renderHoldingDetails(holding)}
              </Card.Content>
            </Card>
          );
        })}
      </div>

      {holdings.length === 0 && (
        <div className="empty-holdings">
          <Card variant="elevated">
            <Card.Content>
              <div className="empty-state">
                <div className="empty-icon">📊</div>
                <h4>No Holdings Found</h4>
                <p>Your stock positions will appear here once you start trading</p>
              </div>
            </Card.Content>
          </Card>
        </div>
      )}
    </div>
  );
};

export default CurrentHoldings;
