import React from 'react';
import './Alert.css';

const Alert = ({
  children,
  variant = 'info',
  size = 'md',
  dismissible = false,
  onDismiss,
  icon,
  title,
  className = '',
  ...props
}) => {
  const baseClasses = 'alert';
  const variantClasses = `alert--${variant}`;
  const sizeClasses = `alert--${size}`;
  const dismissibleClasses = dismissible ? 'alert--dismissible' : '';

  const alertClasses = [
    baseClasses,
    variantClasses,
    sizeClasses,
    dismissibleClasses,
    className
  ].filter(Boolean).join(' ');

  const getDefaultIcon = () => {
    switch (variant) {
      case 'success':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M9 12l2 2 4-4"></path>
            <circle cx="12" cy="12" r="10"></circle>
          </svg>
        );
      case 'warning':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        );
      case 'error':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
        );
      case 'info':
      default:
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 16v-4"></path>
            <path d="M12 8h.01"></path>
          </svg>
        );
    }
  };

  return (
    <div className={alertClasses} role="alert" {...props}>
      <div className="alert__content">
        {(icon || !title) && (
          <div className="alert__icon">
            {icon || getDefaultIcon()}
          </div>
        )}
        
        <div className="alert__body">
          {title && (
            <div className="alert__title">
              {title}
            </div>
          )}
          <div className="alert__message">
            {children}
          </div>
        </div>
      </div>
      
      {dismissible && (
        <button
          className="alert__dismiss"
          onClick={onDismiss}
          aria-label="Dismiss alert"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      )}
    </div>
  );
};

export default Alert;
