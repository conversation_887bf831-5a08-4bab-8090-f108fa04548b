import React, { useState, useEffect } from 'react';
import './SmartAPIConnection.css';

const SmartAPIConnection = ({ onConnectionChange }) => {
  const [credentials, setCredentials] = useState({
    api_key: '',
    client_id: '',
    password: '',
    mpin: '',
    totp: ''
  });
  
  const [connectionStatus, setConnectionStatus] = useState({
    isConnected: false,
    isConnecting: false,
    error: null,
    userInfo: null
  });
  
  const [showCredentials, setShowCredentials] = useState(false);

  useEffect(() => {
    checkConnectionStatus();
  }, []);

  const checkConnectionStatus = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/smartapi/status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConnectionStatus(prev => ({
          ...prev,
          isConnected: data.is_connected,
          userInfo: data.user_info,
          error: data.error
        }));
        
        if (onConnectionChange) {
          onConnectionChange(data.is_connected);
        }
      }
    } catch (error) {
      console.error('Error checking connection status:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCredentials(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleConnect = async (e) => {
    e.preventDefault();
    
    setConnectionStatus(prev => ({
      ...prev,
      isConnecting: true,
      error: null
    }));

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/smartapi/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(credentials)
      });

      const data = await response.json();

      if (data.success) {
        setConnectionStatus({
          isConnected: true,
          isConnecting: false,
          error: null,
          userInfo: {
            name: data.user_name,
            session_expiry: data.session_expiry
          }
        });
        
        setShowCredentials(false);
        
        if (onConnectionChange) {
          onConnectionChange(true);
        }
      } else {
        setConnectionStatus(prev => ({
          ...prev,
          isConnecting: false,
          error: data.error
        }));
      }
    } catch (error) {
      setConnectionStatus(prev => ({
        ...prev,
        isConnecting: false,
        error: 'Connection failed. Please try again.'
      }));
    }
  };

  const handleDisconnect = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/smartapi/disconnect', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        setConnectionStatus({
          isConnected: false,
          isConnecting: false,
          error: null,
          userInfo: null
        });
        
        if (onConnectionChange) {
          onConnectionChange(false);
        }
      }
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  };

  if (connectionStatus.isConnected) {
    return (
      <div className="smartapi-connection connected">
        <div className="connection-header">
          <div className="status-indicator connected">
            <span className="status-dot"></span>
            Connected to Angel One
          </div>
          <button 
            onClick={handleDisconnect}
            className="disconnect-btn"
          >
            Disconnect
          </button>
        </div>
        
        {connectionStatus.userInfo && (
          <div className="user-info">
            <p><strong>Welcome:</strong> {connectionStatus.userInfo.name}</p>
            {connectionStatus.userInfo.session_expiry && (
              <p><strong>Session expires:</strong> {new Date(connectionStatus.userInfo.session_expiry).toLocaleString()}</p>
            )}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="smartapi-connection">
      <div className="connection-header">
        <div className="status-indicator disconnected">
          <span className="status-dot"></span>
          Not connected to Angel One
        </div>
        <button 
          onClick={() => setShowCredentials(!showCredentials)}
          className="connect-btn"
          disabled={connectionStatus.isConnecting}
        >
          {connectionStatus.isConnecting ? 'Connecting...' : 'Connect'}
        </button>
      </div>

      {connectionStatus.error && (
        <div className="error-message">
          {connectionStatus.error}
        </div>
      )}

      {showCredentials && (
        <div className="credentials-form">
          <h3>Enter Angel One Credentials</h3>
          <form onSubmit={handleConnect}>
            <div className="form-group">
              <label>API Key</label>
              <input
                type="text"
                name="api_key"
                value={credentials.api_key}
                onChange={handleInputChange}
                placeholder="Your Angel One API Key"
                required
              />
            </div>

            <div className="form-group">
              <label>Client ID</label>
              <input
                type="text"
                name="client_id"
                value={credentials.client_id}
                onChange={handleInputChange}
                placeholder="Your Angel One Client ID"
                required
              />
            </div>

            <div className="form-group">
              <label>Password</label>
              <input
                type="password"
                name="password"
                value={credentials.password}
                onChange={handleInputChange}
                placeholder="Your Angel One Password"
                required
              />
            </div>

            <div className="form-group">
              <label>MPIN</label>
              <input
                type="password"
                name="mpin"
                value={credentials.mpin}
                onChange={handleInputChange}
                placeholder="Your 4-digit MPIN"
                maxLength="4"
                required
              />
            </div>

            <div className="form-group">
              <label>TOTP Secret</label>
              <input
                type="text"
                name="totp"
                value={credentials.totp}
                onChange={handleInputChange}
                placeholder="Your TOTP Secret Key"
                required
              />
              <small>Get this from Angel One app settings</small>
            </div>

            <div className="form-actions">
              <button 
                type="button" 
                onClick={() => setShowCredentials(false)}
                className="cancel-btn"
              >
                Cancel
              </button>
              <button 
                type="submit" 
                disabled={connectionStatus.isConnecting}
                className="submit-btn"
              >
                {connectionStatus.isConnecting ? 'Connecting...' : 'Connect'}
              </button>
            </div>
          </form>

          <div className="security-note">
            <p><strong>Security Note:</strong> Your credentials are encrypted and stored securely. We never store your password in plain text.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SmartAPIConnection;
