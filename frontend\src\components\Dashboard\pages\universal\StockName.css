/* Stock Name Component Styles */
.stock-name {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Header Section */
.stock-name-header {
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--primary-200);
}

.header-info h3 {
  margin: 0 0 var(--space-2);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
}

.header-info p {
  margin: 0 0 var(--space-4);
  color: var(--secondary-600);
  font-size: var(--text-base);
}

.summary-stats {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

/* Controls Section */
.controls-grid {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-4);
  align-items: center;
}

.search-control {
  min-width: 300px;
}

.tab-controls {
  display: flex;
  gap: var(--space-2);
  background: var(--secondary-50);
  padding: var(--space-2);
  border-radius: var(--radius-lg);
}

.tab-btn {
  padding: var(--space-3) var(--space-4);
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-base);
  font-weight: var(--font-medium);
  color: var(--secondary-600);
  white-space: nowrap;
  font-size: var(--text-sm);
}

.tab-btn:hover {
  background: var(--secondary-100);
  color: var(--secondary-900);
}

.tab-btn.active {
  background: white;
  color: var(--primary-600);
  box-shadow: var(--shadow-sm);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
}

.stocks-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.stocks-table thead {
  background: var(--secondary-50);
}

.stocks-table th {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  border-bottom: 2px solid var(--secondary-200);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.stocks-table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--secondary-100);
  font-size: var(--text-sm);
  color: var(--secondary-700);
  white-space: nowrap;
}

.stock-row {
  transition: all var(--transition-base);
}

.stock-row:hover {
  background: var(--secondary-50);
}

/* Table Cell Styles */
.stock-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.stock-symbol {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  font-size: var(--text-sm);
}

.stock-name {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  line-height: 1.2;
}

.price-cell {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  text-align: right;
}

.pl-cell {
  font-weight: var(--font-bold);
  text-align: right;
}

.pl-cell.positive {
  color: var(--success-600);
}

.pl-cell.negative {
  color: var(--error-600);
}

.market-cap-cell {
  font-weight: var(--font-medium);
  color: var(--secondary-700);
  text-align: right;
}

/* Loading and Error States */
.stock-name-loading,
.stock-name-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: var(--text-4xl);
}

.error-content h3 {
  margin: 0;
  color: var(--error-600);
  font-size: var(--text-lg);
}

.error-content p {
  margin: 0;
  color: var(--secondary-600);
}

/* Empty State */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
}

.empty-icon {
  font-size: var(--text-4xl);
  color: var(--secondary-400);
}

.empty-content h4 {
  margin: 0;
  color: var(--secondary-700);
  font-size: var(--text-lg);
}

.empty-content p {
  margin: 0;
  color: var(--secondary-600);
  max-width: 400px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .controls-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .tab-controls {
    justify-content: center;
  }
  
  .summary-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .stock-name-header {
    padding: var(--space-4);
    text-align: center;
  }
  
  .tab-controls {
    flex-direction: column;
    gap: var(--space-1);
  }
  
  .tab-btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }
  
  .stocks-table th,
  .stocks-table td {
    padding: var(--space-3) var(--space-4);
  }
  
  .stock-name {
    display: none;
  }
}

@media (max-width: 640px) {
  .header-info h3 {
    font-size: var(--text-lg);
  }
  
  .stocks-table {
    font-size: var(--text-xs);
  }
  
  .stocks-table th,
  .stocks-table td {
    padding: var(--space-2) var(--space-3);
  }
  
  /* Hide less important columns on mobile */
  .stocks-table th:nth-child(6),
  .stocks-table td:nth-child(6),
  .stocks-table th:nth-child(7),
  .stocks-table td:nth-child(7) {
    display: none;
  }
}
