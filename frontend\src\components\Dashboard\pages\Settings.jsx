import React, { useState } from 'react';
import { <PERSON>, But<PERSON>, Input, Bad<PERSON>, <PERSON><PERSON>, PageHeader } from '../../UI';
import './Settings.css';

const Settings = ({ user, userSubscription }) => {
  const [activeTab, setActiveTab] = useState('profile');
  
  const [profileSettings, setProfileSettings] = useState({
    name: user?.name || 'Demo User',
    email: user?.email || '<EMAIL>',
    phone: '+91 9876543210',
    timezone: 'Asia/Kolkata',
    language: 'en'
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailAlerts: true,
    smsAlerts: false,
    pushNotifications: true,
    signalAlerts: true,
    orderUpdates: true,
    marketNews: false,
    weeklyReports: true
  });

  const [tradingSettings, setTradingSettings] = useState({
    defaultOrderType: 'MARKET',
    defaultQuantity: 10,
    autoStopLoss: true,
    stopLossPercent: 5,
    autoTarget: true,
    targetPercent: 10,
    maxDailyLoss: 5,
    maxPositions: 10
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: 30,
    loginAlerts: true,
    deviceTracking: true
  });

  const handleSaveProfile = () => {
    // API call to save profile settings
    console.log('Saving profile settings:', profileSettings);
    alert('Profile settings saved successfully!');
  };

  const handleSaveNotifications = () => {
    // API call to save notification settings
    console.log('Saving notification settings:', notificationSettings);
    alert('Notification settings saved successfully!');
  };

  const handleSaveTrading = () => {
    // API call to save trading settings
    console.log('Saving trading settings:', tradingSettings);
    alert('Trading settings saved successfully!');
  };

  const handleSaveSecurity = () => {
    // API call to save security settings
    console.log('Saving security settings:', securitySettings);
    alert('Security settings saved successfully!');
  };

  const getSubscriptionBadge = () => {
    const badges = {
      'starter': { label: 'Starter', color: '#10b981' },
      'professional': { label: 'Professional', color: '#3b82f6' },
      'enterprise': { label: 'Enterprise', color: '#8b5cf6' }
    };
    return badges[userSubscription] || badges.starter;
  };

  const subscriptionBadge = getSubscriptionBadge();

  return (
    <div className="settings-page">
      <PageHeader
        title="Settings"
        subtitle="Manage your account preferences and trading settings"
        variant="default"
      />

      <div className="settings-container">
        {/* Settings Navigation */}
        <div className="settings-nav">
          <button 
            className={`nav-item ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            <span className="nav-icon">👤</span>
            Profile
          </button>
          <button 
            className={`nav-item ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => setActiveTab('notifications')}
          >
            <span className="nav-icon">🔔</span>
            Notifications
          </button>
          <button 
            className={`nav-item ${activeTab === 'trading' ? 'active' : ''}`}
            onClick={() => setActiveTab('trading')}
          >
            <span className="nav-icon">📈</span>
            Trading
          </button>
          <button 
            className={`nav-item ${activeTab === 'security' ? 'active' : ''}`}
            onClick={() => setActiveTab('security')}
          >
            <span className="nav-icon">🔒</span>
            Security
          </button>
          <button 
            className={`nav-item ${activeTab === 'subscription' ? 'active' : ''}`}
            onClick={() => setActiveTab('subscription')}
          >
            <span className="nav-icon">💳</span>
            Subscription
          </button>
        </div>

        {/* Settings Content */}
        <div className="settings-content">
          {activeTab === 'profile' && (
            <div className="settings-section">
              <h2>Profile Settings</h2>
              <p>Manage your personal information and preferences</p>

              <div className="settings-form">
                <div className="form-group">
                  <label>Full Name</label>
                  <input 
                    type="text"
                    value={profileSettings.name}
                    onChange={(e) => setProfileSettings(prev => ({
                      ...prev,
                      name: e.target.value
                    }))}
                    className="settings-input"
                  />
                </div>

                <div className="form-group">
                  <label>Email Address</label>
                  <input 
                    type="email"
                    value={profileSettings.email}
                    onChange={(e) => setProfileSettings(prev => ({
                      ...prev,
                      email: e.target.value
                    }))}
                    className="settings-input"
                  />
                </div>

                <div className="form-group">
                  <label>Phone Number</label>
                  <input 
                    type="tel"
                    value={profileSettings.phone}
                    onChange={(e) => setProfileSettings(prev => ({
                      ...prev,
                      phone: e.target.value
                    }))}
                    className="settings-input"
                  />
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Timezone</label>
                    <select 
                      value={profileSettings.timezone}
                      onChange={(e) => setProfileSettings(prev => ({
                        ...prev,
                        timezone: e.target.value
                      }))}
                      className="settings-select"
                    >
                      <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                      <option value="America/New_York">America/New_York (EST)</option>
                      <option value="Europe/London">Europe/London (GMT)</option>
                      <option value="Asia/Tokyo">Asia/Tokyo (JST)</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Language</label>
                    <select 
                      value={profileSettings.language}
                      onChange={(e) => setProfileSettings(prev => ({
                        ...prev,
                        language: e.target.value
                      }))}
                      className="settings-select"
                    >
                      <option value="en">English</option>
                      <option value="hi">Hindi</option>
                      <option value="mr">Marathi</option>
                      <option value="gu">Gujarati</option>
                    </select>
                  </div>
                </div>

                <button className="save-btn" onClick={handleSaveProfile}>
                  Save Profile Settings
                </button>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="settings-section">
              <h2>Notification Settings</h2>
              <p>Choose how you want to receive alerts and updates</p>

              <div className="settings-form">
                <div className="notification-group">
                  <h3>Alert Channels</h3>
                  <div className="toggle-group">
                    <label className="toggle-label">
                      <input 
                        type="checkbox"
                        checked={notificationSettings.emailAlerts}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          emailAlerts: e.target.checked
                        }))}
                      />
                      <span className="toggle-slider"></span>
                      Email Alerts
                    </label>
                    <label className="toggle-label">
                      <input 
                        type="checkbox"
                        checked={notificationSettings.smsAlerts}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          smsAlerts: e.target.checked
                        }))}
                      />
                      <span className="toggle-slider"></span>
                      SMS Alerts
                    </label>
                    <label className="toggle-label">
                      <input 
                        type="checkbox"
                        checked={notificationSettings.pushNotifications}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          pushNotifications: e.target.checked
                        }))}
                      />
                      <span className="toggle-slider"></span>
                      Push Notifications
                    </label>
                  </div>
                </div>

                <div className="notification-group">
                  <h3>Trading Alerts</h3>
                  <div className="toggle-group">
                    <label className="toggle-label">
                      <input 
                        type="checkbox"
                        checked={notificationSettings.signalAlerts}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          signalAlerts: e.target.checked
                        }))}
                      />
                      <span className="toggle-slider"></span>
                      Signal Alerts
                    </label>
                    <label className="toggle-label">
                      <input 
                        type="checkbox"
                        checked={notificationSettings.orderUpdates}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          orderUpdates: e.target.checked
                        }))}
                      />
                      <span className="toggle-slider"></span>
                      Order Updates
                    </label>
                    <label className="toggle-label">
                      <input 
                        type="checkbox"
                        checked={notificationSettings.marketNews}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          marketNews: e.target.checked
                        }))}
                      />
                      <span className="toggle-slider"></span>
                      Market News
                    </label>
                    <label className="toggle-label">
                      <input 
                        type="checkbox"
                        checked={notificationSettings.weeklyReports}
                        onChange={(e) => setNotificationSettings(prev => ({
                          ...prev,
                          weeklyReports: e.target.checked
                        }))}
                      />
                      <span className="toggle-slider"></span>
                      Weekly Reports
                    </label>
                  </div>
                </div>

                <button className="save-btn" onClick={handleSaveNotifications}>
                  Save Notification Settings
                </button>
              </div>
            </div>
          )}

          {activeTab === 'trading' && (
            <div className="settings-section">
              <h2>Trading Settings</h2>
              <p>Configure your default trading parameters</p>

              <div className="settings-form">
                <div className="form-row">
                  <div className="form-group">
                    <label>Default Order Type</label>
                    <select 
                      value={tradingSettings.defaultOrderType}
                      onChange={(e) => setTradingSettings(prev => ({
                        ...prev,
                        defaultOrderType: e.target.value
                      }))}
                      className="settings-select"
                    >
                      <option value="MARKET">Market</option>
                      <option value="LIMIT">Limit</option>
                    </select>
                  </div>

                  <div className="form-group">
                    <label>Default Quantity</label>
                    <input 
                      type="number"
                      value={tradingSettings.defaultQuantity}
                      onChange={(e) => setTradingSettings(prev => ({
                        ...prev,
                        defaultQuantity: parseInt(e.target.value)
                      }))}
                      min="1"
                      className="settings-input"
                    />
                  </div>
                </div>

                <div className="toggle-group">
                  <label className="toggle-label">
                    <input 
                      type="checkbox"
                      checked={tradingSettings.autoStopLoss}
                      onChange={(e) => setTradingSettings(prev => ({
                        ...prev,
                        autoStopLoss: e.target.checked
                      }))}
                    />
                    <span className="toggle-slider"></span>
                    Auto Stop Loss
                  </label>
                  {tradingSettings.autoStopLoss && (
                    <div className="sub-setting">
                      <label>Stop Loss %</label>
                      <input 
                        type="number"
                        value={tradingSettings.stopLossPercent}
                        onChange={(e) => setTradingSettings(prev => ({
                          ...prev,
                          stopLossPercent: parseFloat(e.target.value)
                        }))}
                        min="1"
                        max="20"
                        step="0.5"
                        className="settings-input small"
                      />
                    </div>
                  )}
                </div>

                <div className="toggle-group">
                  <label className="toggle-label">
                    <input 
                      type="checkbox"
                      checked={tradingSettings.autoTarget}
                      onChange={(e) => setTradingSettings(prev => ({
                        ...prev,
                        autoTarget: e.target.checked
                      }))}
                    />
                    <span className="toggle-slider"></span>
                    Auto Target
                  </label>
                  {tradingSettings.autoTarget && (
                    <div className="sub-setting">
                      <label>Target %</label>
                      <input 
                        type="number"
                        value={tradingSettings.targetPercent}
                        onChange={(e) => setTradingSettings(prev => ({
                          ...prev,
                          targetPercent: parseFloat(e.target.value)
                        }))}
                        min="1"
                        max="50"
                        step="0.5"
                        className="settings-input small"
                      />
                    </div>
                  )}
                </div>

                <div className="form-row">
                  <div className="form-group">
                    <label>Max Daily Loss (%)</label>
                    <input 
                      type="number"
                      value={tradingSettings.maxDailyLoss}
                      onChange={(e) => setTradingSettings(prev => ({
                        ...prev,
                        maxDailyLoss: parseFloat(e.target.value)
                      }))}
                      min="1"
                      max="20"
                      step="0.5"
                      className="settings-input"
                    />
                  </div>

                  <div className="form-group">
                    <label>Max Positions</label>
                    <input 
                      type="number"
                      value={tradingSettings.maxPositions}
                      onChange={(e) => setTradingSettings(prev => ({
                        ...prev,
                        maxPositions: parseInt(e.target.value)
                      }))}
                      min="1"
                      max="50"
                      className="settings-input"
                    />
                  </div>
                </div>

                <button className="save-btn" onClick={handleSaveTrading}>
                  Save Trading Settings
                </button>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="settings-section">
              <h2>Security Settings</h2>
              <p>Manage your account security and privacy</p>

              <div className="settings-form">
                <div className="toggle-group">
                  <label className="toggle-label">
                    <input 
                      type="checkbox"
                      checked={securitySettings.twoFactorAuth}
                      onChange={(e) => setSecuritySettings(prev => ({
                        ...prev,
                        twoFactorAuth: e.target.checked
                      }))}
                    />
                    <span className="toggle-slider"></span>
                    Two-Factor Authentication
                  </label>
                  <label className="toggle-label">
                    <input 
                      type="checkbox"
                      checked={securitySettings.loginAlerts}
                      onChange={(e) => setSecuritySettings(prev => ({
                        ...prev,
                        loginAlerts: e.target.checked
                      }))}
                    />
                    <span className="toggle-slider"></span>
                    Login Alerts
                  </label>
                  <label className="toggle-label">
                    <input 
                      type="checkbox"
                      checked={securitySettings.deviceTracking}
                      onChange={(e) => setSecuritySettings(prev => ({
                        ...prev,
                        deviceTracking: e.target.checked
                      }))}
                    />
                    <span className="toggle-slider"></span>
                    Device Tracking
                  </label>
                </div>

                <div className="form-group">
                  <label>Session Timeout (minutes)</label>
                  <select 
                    value={securitySettings.sessionTimeout}
                    onChange={(e) => setSecuritySettings(prev => ({
                      ...prev,
                      sessionTimeout: parseInt(e.target.value)
                    }))}
                    className="settings-select"
                  >
                    <option value={15}>15 minutes</option>
                    <option value={30}>30 minutes</option>
                    <option value={60}>1 hour</option>
                    <option value={120}>2 hours</option>
                    <option value={480}>8 hours</option>
                  </select>
                </div>

                <div className="security-actions">
                  <button className="secondary-btn">Change Password</button>
                  <button className="secondary-btn">Download Data</button>
                  <button className="danger-btn">Delete Account</button>
                </div>

                <button className="save-btn" onClick={handleSaveSecurity}>
                  Save Security Settings
                </button>
              </div>
            </div>
          )}

          {activeTab === 'subscription' && (
            <div className="settings-section">
              <h2>Subscription Management</h2>
              <p>Manage your subscription plan and billing</p>

              <div className="subscription-info">
                <div className="current-plan">
                  <h3>Current Plan</h3>
                  <div className="plan-details">
                    <span 
                      className="plan-badge"
                      style={{ backgroundColor: subscriptionBadge.color }}
                    >
                      {subscriptionBadge.label}
                    </span>
                    <div className="plan-info">
                      <p><strong>Status:</strong> Active</p>
                      <p><strong>Next Billing:</strong> February 15, 2024</p>
                      <p><strong>Amount:</strong> ₹{userSubscription === 'starter' ? '2,999' : userSubscription === 'professional' ? '5,999' : '12,999'}/month</p>
                    </div>
                  </div>
                </div>

                <div className="plan-actions">
                  {userSubscription !== 'enterprise' && (
                    <button className="upgrade-btn">
                      Upgrade Plan
                    </button>
                  )}
                  <button className="secondary-btn">View Billing History</button>
                  <button className="secondary-btn">Update Payment Method</button>
                  <button className="danger-btn">Cancel Subscription</button>
                </div>

                <div className="billing-info">
                  <h3>Billing Information</h3>
                  <div className="billing-details">
                    <p><strong>Payment Method:</strong> •••• •••• •••• 1234</p>
                    <p><strong>Billing Address:</strong> Mumbai, Maharashtra, India</p>
                    <p><strong>Tax ID:</strong> GST123456789</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
