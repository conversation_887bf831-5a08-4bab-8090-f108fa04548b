/* BOH Filter Styles */
.boh-filter {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Header Section */
.boh-filter-header {
  background: linear-gradient(135deg, var(--primary-50), var(--success-50));
  padding: var(--space-6);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--primary-200);
}

.filter-summary h3 {
  margin: 0 0 var(--space-2);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--secondary-900);
}

.filter-summary p {
  margin: 0 0 var(--space-4);
  color: var(--secondary-600);
  font-size: var(--text-base);
}

.summary-stats {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

/* Filter Controls */
.filter-controls-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-4);
  align-items: end;
}

.filter-control {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.filter-control label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--secondary-700);
}

.filter-control select {
  padding: var(--space-3);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-lg);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-900);
  transition: all var(--transition-base);
}

.filter-control select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
}

.boh-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.boh-table thead {
  background: var(--secondary-50);
}

.boh-table th {
  padding: var(--space-4) var(--space-6);
  text-align: left;
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  border-bottom: 2px solid var(--secondary-200);
  font-size: var(--text-sm);
}

.boh-table td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--secondary-100);
  font-size: var(--text-sm);
  color: var(--secondary-700);
}

.boh-table tbody tr {
  transition: all var(--transition-base);
}

.boh-table tbody tr:hover {
  background: var(--secondary-50);
}

.boh-table tbody tr.boh-eligible {
  background: var(--success-50);
}

.boh-table tbody tr.boh-eligible:hover {
  background: var(--success-100);
}

/* Stock Info Cell */
.stock-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.stock-symbol {
  font-weight: var(--font-semibold);
  color: var(--secondary-900);
  font-size: var(--text-sm);
}

.stock-name {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  line-height: 1.2;
}

/* Price Cell */
.price-cell {
  font-weight: var(--font-semibold);
  color: var(--primary-600);
  text-align: right;
}

/* Date Cell */
.date-cell {
  font-family: var(--font-family-mono);
  font-size: var(--text-xs);
  color: var(--secondary-600);
}

/* Eligibility Cell */
.eligibility-cell {
  text-align: center;
}

/* Loading State */
.boh-filter-loading,
.boh-filter-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  text-align: center;
  padding: var(--space-8);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--secondary-200);
  border-top: 4px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: var(--text-4xl);
}

.error-content h3 {
  margin: 0;
  color: var(--error-600);
  font-size: var(--text-lg);
}

.error-content p {
  margin: 0;
  color: var(--secondary-600);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filter-controls-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3);
  }
  
  .search-control {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .filter-controls-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-stats {
    justify-content: center;
  }
  
  .boh-table th,
  .boh-table td {
    padding: var(--space-3) var(--space-4);
  }
  
  .stock-name {
    display: none;
  }
}

@media (max-width: 640px) {
  .boh-filter-header {
    padding: var(--space-4);
    text-align: center;
  }
  
  .filter-summary h3 {
    font-size: var(--text-lg);
  }
  
  .boh-table {
    font-size: var(--text-xs);
  }
  
  .boh-table th,
  .boh-table td {
    padding: var(--space-2) var(--space-3);
  }
  
  /* Hide less important columns on mobile */
  .boh-table th:nth-child(3),
  .boh-table td:nth-child(3),
  .boh-table th:nth-child(4),
  .boh-table td:nth-child(4) {
    display: none;
  }
}
