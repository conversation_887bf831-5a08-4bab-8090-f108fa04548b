/* Button Component */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-family: var(--font-family-sans);
  font-weight: var(--font-medium);
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  user-select: none;
  white-space: nowrap;
  outline: none;
}

.btn:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* But<PERSON> Sizes */
.btn--xs {
  height: 32px;
  padding: 0 var(--space-3);
  font-size: var(--text-xs);
  line-height: var(--leading-tight);
}

.btn--sm {
  height: 36px;
  padding: 0 var(--space-4);
  font-size: var(--text-sm);
  line-height: var(--leading-tight);
}

.btn--md {
  height: var(--button-height);
  padding: 0 var(--space-5);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

.btn--lg {
  height: 52px;
  padding: 0 var(--space-6);
  font-size: var(--text-lg);
  line-height: var(--leading-normal);
}

.btn--xl {
  height: 60px;
  padding: 0 var(--space-8);
  font-size: var(--text-xl);
  line-height: var(--leading-normal);
}

/* Button Variants */
.btn--primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border-color: var(--primary-600);
  box-shadow: var(--shadow-sm);
}

.btn--primary:hover:not(.btn--disabled):not(.btn--loading) {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  border-color: var(--primary-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn--primary:active:not(.btn--disabled):not(.btn--loading) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn--secondary {
  background: white;
  color: var(--secondary-700);
  border-color: var(--secondary-300);
  box-shadow: var(--shadow-sm);
}

.btn--secondary:hover:not(.btn--disabled):not(.btn--loading) {
  background: var(--secondary-50);
  border-color: var(--secondary-400);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn--secondary:active:not(.btn--disabled):not(.btn--loading) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn--outline {
  background: transparent;
  color: var(--primary-600);
  border-color: var(--primary-300);
}

.btn--outline:hover:not(.btn--disabled):not(.btn--loading) {
  background: var(--primary-50);
  border-color: var(--primary-400);
  color: var(--primary-700);
}

.btn--ghost {
  background: transparent;
  color: var(--secondary-600);
  border-color: transparent;
}

.btn--ghost:hover:not(.btn--disabled):not(.btn--loading) {
  background: var(--secondary-100);
  color: var(--secondary-700);
}

.btn--success {
  background: linear-gradient(135deg, var(--success-600), var(--success-700));
  color: white;
  border-color: var(--success-600);
  box-shadow: var(--shadow-sm);
}

.btn--success:hover:not(.btn--disabled):not(.btn--loading) {
  background: linear-gradient(135deg, var(--success-700), var(--success-800));
  border-color: var(--success-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn--warning {
  background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  color: white;
  border-color: var(--warning-500);
  box-shadow: var(--shadow-sm);
}

.btn--warning:hover:not(.btn--disabled):not(.btn--loading) {
  background: linear-gradient(135deg, var(--warning-600), var(--warning-700));
  border-color: var(--warning-600);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn--error {
  background: linear-gradient(135deg, var(--error-600), var(--error-700));
  color: white;
  border-color: var(--error-600);
  box-shadow: var(--shadow-sm);
}

.btn--error:hover:not(.btn--disabled):not(.btn--loading) {
  background: linear-gradient(135deg, var(--error-700), var(--error-800));
  border-color: var(--error-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Button States */
.btn--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn--loading {
  cursor: wait;
  pointer-events: none;
}

.btn--loading .btn__content {
  opacity: 0.7;
}

.btn--full-width {
  width: 100%;
}

/* Button Elements */
.btn__content {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity var(--transition-base);
}

.btn__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.btn__icon--left {
  margin-right: var(--space-1);
  margin-left: calc(var(--space-1) * -1);
}

.btn__icon--right {
  margin-left: var(--space-1);
  margin-right: calc(var(--space-1) * -1);
}

.btn__icon svg {
  width: 1.25em;
  height: 1.25em;
}

/* Spinner */
.btn__spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn__spinner-icon {
  width: 1.25em;
  height: 1.25em;
  animation: btn-spin 1s linear infinite;
}

.btn__spinner-circle {
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
  animation: btn-dash 2s ease-in-out infinite;
}

@keyframes btn-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes btn-dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .btn--xs {
    height: 28px;
    padding: 0 var(--space-2);
    font-size: 0.7rem;
  }
  
  .btn--sm {
    height: 32px;
    padding: 0 var(--space-3);
    font-size: var(--text-xs);
  }
  
  .btn--md {
    height: 40px;
    padding: 0 var(--space-4);
    font-size: var(--text-sm);
  }
  
  .btn--lg {
    height: 48px;
    padding: 0 var(--space-5);
    font-size: var(--text-base);
  }
  
  .btn--xl {
    height: 56px;
    padding: 0 var(--space-6);
    font-size: var(--text-lg);
  }
}
