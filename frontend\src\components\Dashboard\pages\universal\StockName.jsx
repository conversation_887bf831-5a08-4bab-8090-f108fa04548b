import React, { useState, useEffect } from 'react';
import { Card, Button, Badge, Input } from '../../../UI';
import { fetchNifty200StockData } from '../../../../services/yahooFinanceService';
import './StockName.css';

const StockName = () => {
  const [stocks, setStocks] = useState([]);
  const [filteredStocks, setFilteredStocks] = useState([]);
  const [tradingStocks, setTradingStocks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('regular');

  // Mock current holdings for filtering (replace with actual data from strategies)
  const [currentHoldings] = useState([
    { symbol: 'RELIANCE', averagePrice: 1950.00, currentPrice: 2505.00 },
    { symbol: 'TCS', averagePrice: 1800.00, currentPrice: 3920.00 },
    { symbol: 'INFY', averagePrice: 1200.00, currentPrice: 1420.00 }
  ]);

  useEffect(() => {
    fetchStockData();
  }, []);

  useEffect(() => {
    filterStocks();
  }, [stocks, searchTerm, activeTab]);

  const fetchStockData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const stockData = await fetchNifty200StockData();
      setStocks(stockData);
      
      // Separate trading stocks (owned stocks that crossed ₹2000)
      const tradingStocksList = stockData.filter(stock => {
        const holding = currentHoldings.find(h => h.symbol === stock.symbol);
        return holding && stock.currentPrice > 2000;
      });
      
      setTradingStocks(tradingStocksList);
    } catch (err) {
      setError('Failed to fetch stock data. Please try again.');
      console.error('Stock Name fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterStocks = () => {
    let filtered = [];
    
    if (activeTab === 'regular') {
      // Regular stocks: Nifty 200 stocks under ₹2000
      filtered = stocks.filter(stock => stock.currentPrice < 2000);
    } else {
      // Trading stocks: Owned stocks that crossed ₹2000
      filtered = tradingStocks;
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(stock =>
        stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort by stock name
    filtered.sort((a, b) => a.symbol.localeCompare(b.symbol));

    setFilteredStocks(filtered);
  };

  const formatPrice = (price) => {
    return `₹${price?.toFixed(2) || '0.00'}`;
  };

  const getHoldingInfo = (symbol) => {
    return currentHoldings.find(h => h.symbol === symbol);
  };

  const calculateProfitLoss = (stock) => {
    const holding = getHoldingInfo(stock.symbol);
    if (!holding) return null;
    
    const plAmount = stock.currentPrice - holding.averagePrice;
    const plPercent = (plAmount / holding.averagePrice) * 100;
    
    return { amount: plAmount, percent: plPercent };
  };

  if (loading) {
    return (
      <div className="stock-name-loading">
        <Card variant="elevated">
          <Card.Content>
            <div className="loading-content">
              <div className="loading-spinner"></div>
              <p>Loading Nifty 200 stock data...</p>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="stock-name-error">
        <Card variant="elevated">
          <Card.Content>
            <div className="error-content">
              <div className="error-icon">⚠️</div>
              <h3>Error Loading Data</h3>
              <p>{error}</p>
              <Button onClick={fetchStockData} variant="primary">
                Retry
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="stock-name">
      <div className="stock-name-header">
        <div className="header-info">
          <h3>Stock Name - Nifty 200 Universe</h3>
          <p>Browse stocks by price categories and trading status</p>
          <div className="summary-stats">
            <Badge variant="primary">
              Regular Stocks (Under ₹2000): {stocks.filter(s => s.currentPrice < 2000).length}
            </Badge>
            <Badge variant="success">
              Trading Stocks (Owned, Above ₹2000): {tradingStocks.length}
            </Badge>
          </div>
        </div>
      </div>

      <div className="stock-name-controls">
        <Card variant="elevated">
          <Card.Content>
            <div className="controls-grid">
              <div className="search-control">
                <Input
                  type="text"
                  placeholder="Search stocks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="tab-controls">
                <button
                  className={`tab-btn ${activeTab === 'regular' ? 'active' : ''}`}
                  onClick={() => setActiveTab('regular')}
                >
                  Regular Stocks (Under ₹2000)
                </button>
                <button
                  className={`tab-btn ${activeTab === 'trading' ? 'active' : ''}`}
                  onClick={() => setActiveTab('trading')}
                >
                  Trading Stocks (Owned, Above ₹2000)
                </button>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="stock-name-table">
        <Card variant="elevated">
          <Card.Header>
            <Card.Title>
              {activeTab === 'regular' ? 'Regular Stocks' : 'Trading Stocks'} ({filteredStocks.length} stocks)
            </Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="table-container">
              <table className="stocks-table">
                <thead>
                  <tr>
                    <th>Stock Name</th>
                    <th>Current Price</th>
                    {activeTab === 'trading' && <th>Average Price</th>}
                    {activeTab === 'trading' && <th>P/L Amount</th>}
                    {activeTab === 'trading' && <th>P/L %</th>}
                    <th>Market Cap</th>
                    <th>52W High</th>
                    <th>52W Low</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStocks.map((stock) => {
                    const pl = calculateProfitLoss(stock);
                    const holding = getHoldingInfo(stock.symbol);
                    
                    return (
                      <tr key={stock.symbol} className="stock-row">
                        <td>
                          <div className="stock-info">
                            <span className="stock-symbol">{stock.symbol}</span>
                            <span className="stock-name">{stock.name}</span>
                          </div>
                        </td>
                        <td className="price-cell">
                          {formatPrice(stock.currentPrice)}
                        </td>
                        {activeTab === 'trading' && (
                          <td className="price-cell">
                            {formatPrice(holding?.averagePrice)}
                          </td>
                        )}
                        {activeTab === 'trading' && (
                          <td className={`pl-cell ${pl?.amount >= 0 ? 'positive' : 'negative'}`}>
                            {pl ? formatPrice(pl.amount) : 'N/A'}
                          </td>
                        )}
                        {activeTab === 'trading' && (
                          <td className={`pl-cell ${pl?.percent >= 0 ? 'positive' : 'negative'}`}>
                            {pl ? `${pl.percent.toFixed(2)}%` : 'N/A'}
                          </td>
                        )}
                        <td className="market-cap-cell">
                          {stock.marketCap ? `₹${(stock.marketCap / 10000000).toFixed(0)}Cr` : 'N/A'}
                        </td>
                        <td className="price-cell">
                          {formatPrice(stock.fiftyTwoWeekHigh)}
                        </td>
                        <td className="price-cell">
                          {formatPrice(stock.fiftyTwoWeekLow)}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Card.Content>
        </Card>
      </div>

      {filteredStocks.length === 0 && (
        <div className="empty-state">
          <Card variant="elevated">
            <Card.Content>
              <div className="empty-content">
                <div className="empty-icon">📊</div>
                <h4>No Stocks Found</h4>
                <p>
                  {activeTab === 'regular' 
                    ? 'No stocks found under ₹2000 matching your search criteria'
                    : 'No trading stocks found (owned stocks above ₹2000)'
                  }
                </p>
              </div>
            </Card.Content>
          </Card>
        </div>
      )}
    </div>
  );
};

export default StockName;
