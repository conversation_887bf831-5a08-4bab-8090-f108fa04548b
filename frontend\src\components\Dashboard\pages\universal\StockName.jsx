import React, { useState, useEffect } from 'react';
import { Card, Button, Badge, Input } from '../../../UI';
import { fetchNifty200StockData } from '../../../../services/yahooFinanceService';
import './StockName.css';

const StockName = () => {
  const [stocks, setStocks] = useState([]);
  const [filteredStocks, setFilteredStocks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchStockData();
  }, []);

  useEffect(() => {
    filterStocks();
  }, [stocks, searchTerm]);

  const fetchStockData = async () => {
    try {
      setLoading(true);
      setError(null);

      const stockData = await fetchNifty200StockData();
      setStocks(stockData);
    } catch (err) {
      setError('Failed to fetch stock data. Please try again.');
      console.error('Stock Name fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterStocks = () => {
    let filtered = [...stocks];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(stock =>
        stock.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        stock.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort by stock name
    filtered.sort((a, b) => a.symbol.localeCompare(b.symbol));

    setFilteredStocks(filtered);
  };

  const formatPrice = (price) => {
    return `₹${price?.toFixed(2) || '0.00'}`;
  };

  if (loading) {
    return (
      <div className="stock-name-loading">
        <Card variant="elevated">
          <Card.Content>
            <div className="loading-content">
              <div className="loading-spinner"></div>
              <p>Loading Nifty 200 stock data...</p>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="stock-name-error">
        <Card variant="elevated">
          <Card.Content>
            <div className="error-content">
              <div className="error-icon">⚠️</div>
              <h3>Error Loading Data</h3>
              <p>{error}</p>
              <Button onClick={fetchStockData} variant="primary">
                Retry
              </Button>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className="stock-name">
      <div className="stock-name-header">
        <div className="header-info">
          <h3>Stock Name - Nifty 200 Universe</h3>
          <p>Complete list of Nifty 200 stocks with current market price</p>
          <div className="summary-stats">
            <Badge variant="primary">
              Total Stocks: {stocks.length}
            </Badge>
          </div>
        </div>
      </div>

      <div className="stock-name-controls">
        <Card variant="elevated">
          <Card.Content>
            <div className="search-control">
              <Input
                type="text"
                placeholder="Search stocks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="stock-name-table">
        <Card variant="elevated">
          <Card.Header>
            <Card.Title>
              Nifty 200 Stocks ({filteredStocks.length} stocks)
            </Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="table-container">
              <table className="stocks-table">
                <thead>
                  <tr>
                    <th>Stock Name</th>
                    <th>Current Price (CMP)</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStocks.map((stock) => (
                    <tr key={stock.symbol} className="stock-row">
                      <td>
                        <div className="stock-info">
                          <span className="stock-symbol">{stock.symbol}</span>
                          <span className="stock-name">{stock.name}</span>
                        </div>
                      </td>
                      <td className="price-cell">
                        {formatPrice(stock.currentPrice)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card.Content>
        </Card>
      </div>

      {filteredStocks.length === 0 && (
        <div className="empty-state">
          <Card variant="elevated">
            <Card.Content>
              <div className="empty-content">
                <div className="empty-icon">📊</div>
                <h4>No Stocks Found</h4>
                <p>No stocks found matching your search criteria</p>
              </div>
            </Card.Content>
          </Card>
        </div>
      )}
    </div>
  );
};

export default StockName;
