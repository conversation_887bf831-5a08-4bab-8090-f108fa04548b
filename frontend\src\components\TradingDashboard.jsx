import React, { useState, useEffect } from 'react';
import useWebSocket from '../hooks/useWebSocket';
import './TradingDashboard.css';

const TradingDashboard = ({ isSmartAPIConnected }) => {
  const [marketData, setMarketData] = useState({});
  const [portfolio, setPortfolio] = useState({});
  const [watchlist, setWatchlist] = useState([]);
  const [loading, setLoading] = useState(true);

  // WebSocket hook for real-time data
  const {
    connectionStatus,
    marketData: realtimeMarketData,
    orderUpdates,
    subscribeToSymbols,
    unsubscribeFromSymbols,
    isConnected: wsConnected
  } = useWebSocket("user_123", isSmartAPIConnected);

  useEffect(() => {
    fetchDashboardData();
    // Set up real-time updates
    const interval = setInterval(fetchMarketData, 5000);
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch portfolio summary
      const portfolioResponse = await fetch('/api/portfolio/summary');
      const portfolioData = await portfolioResponse.json();
      setPortfolio(portfolioData);

      // Fetch watchlist
      const watchlistResponse = await fetch('/api/portfolio/watchlist');
      const watchlistData = await watchlistResponse.json();
      setWatchlist(watchlistData.watchlist || []);

      // Fetch market status
      const marketResponse = await fetch('/api/market/market-status');
      const marketStatusData = await marketResponse.json();
      setMarketData(marketStatusData);

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchMarketData = async () => {
    try {
      // Update watchlist prices
      const updatedWatchlist = await Promise.all(
        watchlist.map(async (stock) => {
          const response = await fetch(`/api/market/quote/${stock.symbol}`);
          const data = await response.json();
          return { ...stock, ...data };
        })
      );
      setWatchlist(updatedWatchlist);
    } catch (error) {
      console.error('Error updating market data:', error);
    }
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner"></div>
        <p>Loading your trading dashboard...</p>
      </div>
    );
  }

  return (
    <div className="trading-dashboard">
      <header className="dashboard-header">
        <h1>Niveshtor AI Trading Dashboard</h1>
        <div className="market-status">
          <span className={`status-indicator ${marketData.isOpen ? 'open' : 'closed'}`}>
            {marketData.isOpen ? 'Market Open' : 'Market Closed'}
          </span>
        </div>
      </header>

      <div className="dashboard-grid">
        {/* Portfolio Summary */}
        <div className="dashboard-card portfolio-summary">
          <h2>Portfolio Summary</h2>
          <div className="portfolio-metrics">
            <div className="metric">
              <span className="metric-label">Total Value</span>
              <span className="metric-value">₹{portfolio.total_value?.toLocaleString()}</span>
            </div>
            <div className="metric">
              <span className="metric-label">Day P&L</span>
              <span className={`metric-value ${portfolio.day_pnl >= 0 ? 'positive' : 'negative'}`}>
                ₹{portfolio.day_pnl?.toLocaleString()} ({portfolio.day_pnl_percent?.toFixed(2)}%)
              </span>
            </div>
            <div className="metric">
              <span className="metric-label">Total P&L</span>
              <span className={`metric-value ${portfolio.total_pnl >= 0 ? 'positive' : 'negative'}`}>
                ₹{portfolio.total_pnl?.toLocaleString()} ({portfolio.total_pnl_percent?.toFixed(2)}%)
              </span>
            </div>
            <div className="metric">
              <span className="metric-label">Cash Balance</span>
              <span className="metric-value">₹{portfolio.cash_balance?.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Watchlist */}
        <div className="dashboard-card watchlist">
          <h2>Watchlist</h2>
          <div className="watchlist-items">
            {watchlist.map((stock, index) => (
              <div key={index} className="watchlist-item">
                <div className="stock-info">
                  <span className="symbol">{stock.symbol}</span>
                  <span className="name">{stock.name}</span>
                </div>
                <div className="stock-price">
                  <span className="price">₹{stock.price?.toFixed(2)}</span>
                  <span className={`change ${stock.change >= 0 ? 'positive' : 'negative'}`}>
                    {stock.change >= 0 ? '+' : ''}₹{stock.change?.toFixed(2)} ({stock.change_percent?.toFixed(2)}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="dashboard-card quick-actions">
          <h2>Quick Actions</h2>
          <div className="action-buttons">
            <button className="action-btn buy-btn">
              <span>📈</span>
              Buy Stocks
            </button>
            <button className="action-btn sell-btn">
              <span>📉</span>
              Sell Stocks
            </button>
            <button className="action-btn portfolio-btn">
              <span>💼</span>
              View Portfolio
            </button>
            <button className="action-btn orders-btn">
              <span>📋</span>
              Order History
            </button>
          </div>
        </div>

        {/* Market Movers */}
        <div className="dashboard-card market-movers">
          <h2>Market Movers</h2>
          <div className="movers-tabs">
            <button className="tab active">Top Gainers</button>
            <button className="tab">Top Losers</button>
            <button className="tab">Most Active</button>
          </div>
          <div className="movers-list">
            <div className="mover-item">
              <span className="symbol">RELIANCE</span>
              <span className="change positive">+2.45%</span>
            </div>
            <div className="mover-item">
              <span className="symbol">TCS</span>
              <span className="change positive">+1.89%</span>
            </div>
            <div className="mover-item">
              <span className="symbol">INFY</span>
              <span className="change positive">+1.23%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradingDashboard;
