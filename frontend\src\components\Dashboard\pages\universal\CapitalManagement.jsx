import React, { useState, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Badge, PageHeader } from '../../../UI';
import BOHFilter from './BOHFilter';
import WeeklyHighStrategy from './WeeklyHighStrategy';
import GTTOrderManagement from './GTTOrderManagement';
import CurrentHoldings from './CurrentHoldings';
import './CapitalManagement.css';

const CapitalManagement = () => {
  const [activeTab, setActiveTab] = useState('capital-overview');
  const [capitalData, setCapitalData] = useState({
    totalCapital: 100000, // ₹1,00,000
    availableCapital: 85000, // ₹85,000
    investedCapital: 15000, // ₹15,000
    activeStrategies: 2,
    maxStrategiesAllowed: 8, // Based on ₹85,000 / ₹10,000
    perStockLimit: 10000, // ₹10,000
    perTradeLimit: 2000, // ₹2,000
    maxTradesPerStock: 5
  });

  const [strategyStatus, setStrategyStatus] = useState({
    weeklyHigh: { active: true, allocatedCapital: 30000, usedCapital: 8000 },
    rsiStrategy: { active: true, allocatedCapital: 20000, usedCapital: 7000 },
    consolidatedBreakout: { active: false, allocatedCapital: 0, usedCapital: 0 }
  });

  // Calculate strategy activation eligibility
  const canActivateStrategy = () => {
    return capitalData.availableCapital >= capitalData.perStockLimit;
  };

  // Auto-pause/reactivate strategies based on capital
  useEffect(() => {
    const updatedStatus = { ...strategyStatus };
    let hasChanges = false;

    Object.keys(updatedStatus).forEach(strategy => {
      const strategyData = updatedStatus[strategy];
      const remainingCapital = strategyData.allocatedCapital - strategyData.usedCapital;
      
      // Auto-pause if no remaining capital
      if (strategyData.active && remainingCapital < capitalData.perStockLimit) {
        strategyData.active = false;
        hasChanges = true;
      }
      
      // Auto-reactivate if sufficient capital available
      if (!strategyData.active && capitalData.availableCapital >= capitalData.perStockLimit) {
        // Only reactivate if user had previously allocated capital
        if (strategyData.allocatedCapital > 0) {
          strategyData.active = true;
          hasChanges = true;
        }
      }
    });

    if (hasChanges) {
      setStrategyStatus(updatedStatus);
    }
  }, [capitalData, strategyStatus]);

  const tabs = [
    { id: 'capital-overview', label: 'Capital Overview', icon: '💰' },
    { id: 'boh-filter', label: 'BOH Filter', icon: '🔍' },
    { id: 'weekly-high', label: 'Weekly High Strategy', icon: '📈' },
    { id: 'gtt-orders', label: 'GTT Orders', icon: '⚡' },
    { id: 'current-holdings', label: 'Current Holdings', icon: '📊' }
  ];

  const renderCapitalOverview = () => (
    <div className="capital-overview">
      <div className="capital-summary-grid">
        <Card variant="elevated" className="capital-card">
          <Card.Content>
            <div className="capital-metric">
              <div className="metric-icon">💰</div>
              <div className="metric-details">
                <Card.Subtitle>Total Capital</Card.Subtitle>
                <Card.Title>₹{capitalData.totalCapital.toLocaleString()}</Card.Title>
              </div>
            </div>
          </Card.Content>
        </Card>

        <Card variant="elevated" className="capital-card">
          <Card.Content>
            <div className="capital-metric">
              <div className="metric-icon">✅</div>
              <div className="metric-details">
                <Card.Subtitle>Available Capital</Card.Subtitle>
                <Card.Title className="text-success">₹{capitalData.availableCapital.toLocaleString()}</Card.Title>
              </div>
            </div>
          </Card.Content>
        </Card>

        <Card variant="elevated" className="capital-card">
          <Card.Content>
            <div className="capital-metric">
              <div className="metric-icon">📈</div>
              <div className="metric-details">
                <Card.Subtitle>Invested Capital</Card.Subtitle>
                <Card.Title className="text-primary">₹{capitalData.investedCapital.toLocaleString()}</Card.Title>
              </div>
            </div>
          </Card.Content>
        </Card>

        <Card variant="elevated" className="capital-card">
          <Card.Content>
            <div className="capital-metric">
              <div className="metric-icon">⚙️</div>
              <div className="metric-details">
                <Card.Subtitle>Active Strategies</Card.Subtitle>
                <Card.Title>{capitalData.activeStrategies} / {capitalData.maxStrategiesAllowed}</Card.Title>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="capital-rules-section">
        <Card variant="elevated">
          <Card.Header>
            <Card.Title>Capital Management Rules</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="rules-grid">
              <div className="rule-item">
                <div className="rule-icon">🎯</div>
                <div className="rule-content">
                  <h4>Per Stock Limit</h4>
                  <p>Maximum ₹{capitalData.perStockLimit.toLocaleString()} per stock</p>
                </div>
              </div>
              <div className="rule-item">
                <div className="rule-icon">💸</div>
                <div className="rule-content">
                  <h4>Per Trade Limit</h4>
                  <p>Maximum ₹{capitalData.perTradeLimit.toLocaleString()} per trade</p>
                </div>
              </div>
              <div className="rule-item">
                <div className="rule-icon">🔄</div>
                <div className="rule-content">
                  <h4>Trades Per Stock</h4>
                  <p>Maximum {capitalData.maxTradesPerStock} trades per stock</p>
                </div>
              </div>
              <div className="rule-item">
                <div className="rule-icon">⚡</div>
                <div className="rule-content">
                  <h4>Strategy Activation</h4>
                  <p>Minimum ₹{capitalData.perStockLimit.toLocaleString()} required per strategy</p>
                </div>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="strategy-status-section">
        <Card variant="elevated">
          <Card.Header>
            <Card.Title>Strategy Status</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="strategy-status-grid">
              {Object.entries(strategyStatus).map(([key, strategy]) => (
                <div key={key} className="strategy-status-item">
                  <div className="strategy-header">
                    <h4>{key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</h4>
                    <Badge variant={strategy.active ? 'success' : 'secondary'}>
                      {strategy.active ? 'Active' : 'Paused'}
                    </Badge>
                  </div>
                  <div className="strategy-metrics">
                    <div className="metric">
                      <span>Allocated: ₹{strategy.allocatedCapital.toLocaleString()}</span>
                    </div>
                    <div className="metric">
                      <span>Used: ₹{strategy.usedCapital.toLocaleString()}</span>
                    </div>
                    <div className="metric">
                      <span>Available: ₹{(strategy.allocatedCapital - strategy.usedCapital).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'capital-overview':
        return renderCapitalOverview();
      case 'boh-filter':
        return <BOHFilter />;
      case 'weekly-high':
        return <WeeklyHighStrategy />;
      case 'gtt-orders':
        return <GTTOrderManagement />;
      case 'current-holdings':
        return <CurrentHoldings />;
      default:
        return renderCapitalOverview();
    }
  };

  return (
    <div className="capital-management">
      <PageHeader
        title="Capital Management"
        subtitle="Universal trading capital management with ₹10k per stock and ₹2k per trade limits"
        variant="primary"
      />

      <div className="capital-tabs">
        <div className="tab-navigation">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              <span className="tab-label">{tab.label}</span>
            </button>
          ))}
        </div>

        <div className="tab-content">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default CapitalManagement;
