import React, { useState, useEffect } from 'react';
import Card from '../../UI/Card';
import Button from '../../UI/Button';
import './Overview.css';

const Overview = ({ 
  user, 
  isSmartAPIConnected, 
  marketData, 
  orderUpdates, 
  userSubscription,
  onNavigate 
}) => {
  const [portfolioData, setPortfolioData] = useState({
    totalValue: 0,
    totalInvested: 0,
    totalPnL: 0,
    totalPnLPercent: 0,
    dayPnL: 0,
    dayPnLPercent: 0,
    cashBalance: 0
  });

  const [recentSignals, setRecentSignals] = useState([
    {
      id: 1,
      strategy: 'Weekly High',
      symbol: 'RELIANCE',
      action: 'BUY',
      price: 2456.75,
      time: '10:30 AM',
      status: 'active',
      confidence: 85
    },
    {
      id: 2,
      strategy: 'Weekly High',
      symbol: 'TCS',
      action: 'SELL',
      price: 3245.20,
      time: '09:45 AM',
      status: 'executed',
      confidence: 92
    },
    {
      id: 3,
      strategy: 'RSI Strategy',
      symbol: 'INFY',
      action: 'BUY',
      price: 1456.30,
      time: '09:15 AM',
      status: 'pending',
      confidence: 78,
      locked: userSubscription === 'starter'
    }
  ]);

  const [activeStrategies] = useState([
    {
      name: 'Weekly High Strategy',
      status: 'active',
      signals: 12,
      successRate: 85,
      pnl: 15420,
      enabled: true
    },
    {
      name: 'RSI Strategy',
      status: userSubscription === 'starter' ? 'locked' : 'active',
      signals: 8,
      successRate: 78,
      pnl: 8950,
      enabled: userSubscription !== 'starter'
    },
    {
      name: 'Consolidated Breakout',
      status: userSubscription === 'enterprise' ? 'active' : 'locked',
      signals: 5,
      successRate: 92,
      pnl: 22100,
      enabled: userSubscription === 'enterprise'
    }
  ]);

  const [marketOverview] = useState([
    { symbol: 'NIFTY 50', price: 19245.30, change: 1.2, volume: '2.5B' },
    { symbol: 'SENSEX', price: 64832.10, change: 0.8, volume: '1.8B' },
    { symbol: 'BANK NIFTY', price: 43567.80, change: -0.3, volume: '890M' },
    { symbol: 'NIFTY IT', price: 28934.50, change: 2.1, volume: '450M' }
  ]);

  useEffect(() => {
    // Fetch portfolio data if connected
    if (isSmartAPIConnected) {
      fetchPortfolioData();
    }
  }, [isSmartAPIConnected]);

  const fetchPortfolioData = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/portfolio/summary', {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setPortfolioData(data);
      }
    } catch (error) {
      console.error('Error fetching portfolio data:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#10b981';
      case 'executed': return '#3b82f6';
      case 'pending': return '#f59e0b';
      case 'locked': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="overview-page">
      <div className="page-header">
        <h1>Overview</h1>
        <p>Your trading dashboard at a glance</p>
      </div>

      {/* Portfolio Summary */}
      <div className="portfolio-summary">
        <Card variant="elevated" hover className="summary-card">
          <Card.Header>
            <div className="card-header-content">
              <div className="card-icon">💼</div>
              <Card.Subtitle>Total Portfolio Value</Card.Subtitle>
            </div>
          </Card.Header>
          <Card.Content>
            <Card.Title className="metric-value">{formatCurrency(portfolioData.totalValue)}</Card.Title>
            <div className={`change-indicator ${portfolioData.totalPnL >= 0 ? 'positive' : 'negative'}`}>
              <span className="change-icon">{portfolioData.totalPnL >= 0 ? '↗' : '↘'}</span>
              <span>
                {portfolioData.totalPnL >= 0 ? '+' : ''}{formatCurrency(portfolioData.totalPnL)}
                ({portfolioData.totalPnLPercent >= 0 ? '+' : ''}{portfolioData.totalPnLPercent.toFixed(2)}%)
              </span>
            </div>
          </Card.Content>
        </Card>

        <Card variant="elevated" hover className="summary-card">
          <Card.Header>
            <div className="card-header-content">
              <div className="card-icon">📈</div>
              <Card.Subtitle>Today's P&L</Card.Subtitle>
            </div>
          </Card.Header>
          <Card.Content>
            <Card.Title className={`metric-value ${portfolioData.dayPnL >= 0 ? 'positive' : 'negative'}`}>
              {portfolioData.dayPnL >= 0 ? '+' : ''}{formatCurrency(portfolioData.dayPnL)}
            </Card.Title>
            <div className={`change-indicator ${portfolioData.dayPnLPercent >= 0 ? 'positive' : 'negative'}`}>
              <span className="change-icon">{portfolioData.dayPnLPercent >= 0 ? '↗' : '↘'}</span>
              <span>{portfolioData.dayPnLPercent >= 0 ? '+' : ''}{portfolioData.dayPnLPercent.toFixed(2)}%</span>
            </div>
          </Card.Content>
        </Card>

        <Card variant="elevated" hover className="summary-card">
          <Card.Header>
            <div className="card-header-content">
              <div className="card-icon">💰</div>
              <Card.Subtitle>Available Cash</Card.Subtitle>
            </div>
          </Card.Header>
          <Card.Content>
            <Card.Title className="metric-value">{formatCurrency(portfolioData.cashBalance)}</Card.Title>
            <div className="change-indicator neutral">
              <span className="status-text">Ready to invest</span>
            </div>
          </Card.Content>
        </Card>

        <Card variant="elevated" hover className="summary-card">
          <Card.Header>
            <div className="card-header-content">
              <div className="card-icon">🎯</div>
              <Card.Subtitle>Total Invested</Card.Subtitle>
            </div>
          </Card.Header>
          <Card.Content>
            <Card.Title className="metric-value">{formatCurrency(portfolioData.totalInvested)}</Card.Title>
            <div className="change-indicator neutral">
              <span className="status-text">Across all strategies</span>
            </div>
          </Card.Content>
        </Card>
      </div>

      <div className="overview-grid">
        {/* Recent Signals */}
        <div className="overview-section recent-signals">
          <div className="section-header">
            <h2>Recent Signals</h2>
            <button 
              className="view-all-btn"
              onClick={() => onNavigate('weekly-high')}
            >
              View All
            </button>
          </div>
          <div className="signals-list">
            {recentSignals.map(signal => (
              <div key={signal.id} className={`signal-item ${signal.locked ? 'locked' : ''}`}>
                <div className="signal-main">
                  <div className="signal-header">
                    <span className="signal-strategy">{signal.strategy}</span>
                    <span 
                      className="signal-status"
                      style={{ backgroundColor: getStatusColor(signal.status) }}
                    >
                      {signal.locked ? 'Locked' : signal.status}
                    </span>
                  </div>
                  <div className="signal-details">
                    <span className="signal-symbol">{signal.symbol}</span>
                    <span className={`signal-action ${signal.action.toLowerCase()}`}>
                      {signal.action}
                    </span>
                    <span className="signal-price">₹{signal.price}</span>
                  </div>
                </div>
                <div className="signal-meta">
                  <span className="signal-time">{signal.time}</span>
                  <span className="signal-confidence">{signal.confidence}% confidence</span>
                  {signal.locked && <span className="lock-icon">🔒</span>}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Active Strategies */}
        <div className="overview-section active-strategies">
          <div className="section-header">
            <h2>Strategy Performance</h2>
            <button 
              className="view-all-btn"
              onClick={() => onNavigate('strategies')}
            >
              Manage
            </button>
          </div>
          <div className="strategies-list">
            {activeStrategies.map((strategy, index) => (
              <div key={index} className={`strategy-item ${!strategy.enabled ? 'locked' : ''}`}>
                <div className="strategy-header">
                  <span className="strategy-name">{strategy.name}</span>
                  <span 
                    className="strategy-status"
                    style={{ backgroundColor: getStatusColor(strategy.status) }}
                  >
                    {strategy.status}
                  </span>
                </div>
                <div className="strategy-metrics">
                  <div className="metric">
                    <span className="metric-label">Signals</span>
                    <span className="metric-value">{strategy.signals}</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Success Rate</span>
                    <span className="metric-value">{strategy.successRate}%</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">P&L</span>
                    <span className="metric-value positive">+₹{strategy.pnl.toLocaleString()}</span>
                  </div>
                </div>
                {!strategy.enabled && (
                  <div className="strategy-overlay">
                    <span className="lock-icon">🔒</span>
                    <span className="upgrade-text">Upgrade to unlock</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Market Overview */}
        <div className="overview-section market-overview">
          <div className="section-header">
            <h2>Market Overview</h2>
            <span className="market-time">Live • 10:30 AM</span>
          </div>
          <div className="market-list">
            {marketOverview.map((market, index) => (
              <div key={index} className="market-item">
                <div className="market-info">
                  <span className="market-symbol">{market.symbol}</span>
                  <span className="market-volume">{market.volume}</span>
                </div>
                <div className="market-price">
                  <span className="price-value">₹{market.price.toLocaleString()}</span>
                  <span className={`price-change ${market.change >= 0 ? 'positive' : 'negative'}`}>
                    {market.change >= 0 ? '+' : ''}{market.change}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="actions-grid">
          <button 
            className="action-card"
            onClick={() => onNavigate('weekly-high')}
          >
            <span className="action-icon">📈</span>
            <span className="action-label">View Signals</span>
          </button>
          <button 
            className="action-card"
            onClick={() => onNavigate('backtesting')}
          >
            <span className="action-icon">📊</span>
            <span className="action-label">Run Backtest</span>
          </button>
          <button 
            className="action-card"
            onClick={() => onNavigate('tools')}
          >
            <span className="action-icon">🛠️</span>
            <span className="action-label">Trading Tools</span>
          </button>
          <button 
            className="action-card"
            onClick={() => onNavigate('settings')}
          >
            <span className="action-icon">⚙️</span>
            <span className="action-label">Settings</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Overview;
