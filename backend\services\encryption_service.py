"""
Encryption service for secure storage of user credentials
"""

from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class EncryptionService:
    def __init__(self, master_key: Optional[str] = None):
        """Initialize encryption service with master key"""
        if master_key:
            self.master_key = master_key.encode()
        else:
            # Use environment variable or generate a key
            self.master_key = os.environ.get('ENCRYPTION_KEY', 'default-key-change-in-production').encode()
        
        # Derive encryption key from master key
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'niveshtor_ai_salt',  # In production, use random salt per user
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key))
        self.cipher_suite = Fernet(key)
    
    def encrypt(self, data: str) -> str:
        """Encrypt sensitive data"""
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Encryption error: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Decryption error: {e}")
            raise
    
    def encrypt_credentials(self, credentials: dict) -> dict:
        """Encrypt user credentials"""
        encrypted_creds = {}
        sensitive_fields = ['api_key', 'password', 'mpin', 'totp']
        
        for key, value in credentials.items():
            if key in sensitive_fields and value:
                encrypted_creds[f"{key}_encrypted"] = self.encrypt(str(value))
            else:
                encrypted_creds[key] = value
        
        return encrypted_creds
    
    def decrypt_credentials(self, encrypted_credentials: dict) -> dict:
        """Decrypt user credentials"""
        decrypted_creds = {}
        
        for key, value in encrypted_credentials.items():
            if key.endswith('_encrypted') and value:
                original_key = key.replace('_encrypted', '')
                decrypted_creds[original_key] = self.decrypt(value)
            elif not key.endswith('_encrypted'):
                decrypted_creds[key] = value
        
        return decrypted_creds

# Global encryption service instance
encryption_service = EncryptionService()
